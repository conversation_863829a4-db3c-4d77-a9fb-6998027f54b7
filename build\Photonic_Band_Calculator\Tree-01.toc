('C:\\Python\\Python310\\tcl\\tk8.6',
 'tk',
 ['demos', '*.lib', 'tkConfig.sh'],
 'DATA',
 [('tk\\bgerror.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\bgerror.tcl', 'DATA'),
  ('tk\\button.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\button.tcl', 'DATA'),
  ('tk\\choosedir.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('tk\\clrpick.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\clrpick.tcl', 'DATA'),
  ('tk\\comdlg.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\comdlg.tcl', 'DATA'),
  ('tk\\console.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\console.tcl', 'DATA'),
  ('tk\\dialog.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\dialog.tcl', 'DATA'),
  ('tk\\entry.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\entry.tcl', 'DATA'),
  ('tk\\focus.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\focus.tcl', 'DATA'),
  ('tk\\fontchooser.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('tk\\iconlist.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('tk\\icons.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\icons.tcl', 'DATA'),
  ('tk\\license.terms',
   'C:\\Python\\Python310\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('tk\\listbox.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\listbox.tcl', 'DATA'),
  ('tk\\megawidget.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('tk\\menu.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\menu.tcl', 'DATA'),
  ('tk\\mkpsenc.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\mkpsenc.tcl', 'DATA'),
  ('tk\\msgbox.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\msgbox.tcl', 'DATA'),
  ('tk\\obsolete.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('tk\\optMenu.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\optMenu.tcl', 'DATA'),
  ('tk\\palette.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\palette.tcl', 'DATA'),
  ('tk\\panedwindow.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('tk\\pkgIndex.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('tk\\safetk.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\safetk.tcl', 'DATA'),
  ('tk\\scale.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\scale.tcl', 'DATA'),
  ('tk\\scrlbar.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\scrlbar.tcl', 'DATA'),
  ('tk\\spinbox.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\spinbox.tcl', 'DATA'),
  ('tk\\tclIndex', 'C:\\Python\\Python310\\tcl\\tk8.6\\tclIndex', 'DATA'),
  ('tk\\tearoff.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\tearoff.tcl', 'DATA'),
  ('tk\\text.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\text.tcl', 'DATA'),
  ('tk\\tk.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('tk\\tkfbox.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\tkfbox.tcl', 'DATA'),
  ('tk\\unsupported.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('tk\\xmfbox.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\xmfbox.tcl', 'DATA'),
  ('tk\\ttk\\altTheme.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('tk\\ttk\\aquaTheme.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('tk\\ttk\\button.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('tk\\ttk\\clamTheme.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('tk\\ttk\\classicTheme.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('tk\\ttk\\combobox.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('tk\\ttk\\cursors.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('tk\\ttk\\defaults.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('tk\\ttk\\entry.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('tk\\ttk\\fonts.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('tk\\ttk\\menubutton.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('tk\\ttk\\notebook.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('tk\\ttk\\panedwindow.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('tk\\ttk\\progress.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('tk\\ttk\\scale.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('tk\\ttk\\scrollbar.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('tk\\ttk\\sizegrip.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('tk\\ttk\\spinbox.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('tk\\ttk\\treeview.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('tk\\ttk\\ttk.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('tk\\ttk\\utils.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('tk\\ttk\\vistaTheme.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('tk\\ttk\\winTheme.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('tk\\ttk\\xpTheme.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('tk\\msgs\\cs.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('tk\\msgs\\da.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('tk\\msgs\\de.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('tk\\msgs\\el.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('tk\\msgs\\en.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('tk\\msgs\\en_gb.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tk\\msgs\\eo.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('tk\\msgs\\es.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('tk\\msgs\\fr.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('tk\\msgs\\hu.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('tk\\msgs\\it.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('tk\\msgs\\nl.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('tk\\msgs\\pl.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('tk\\msgs\\pt.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('tk\\msgs\\ru.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('tk\\msgs\\sv.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('tk\\images\\logo.eps',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('tk\\images\\logo100.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('tk\\images\\logo64.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('tk\\images\\logoLarge.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('tk\\images\\logoMed.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('tk\\images\\pwrdLogo.eps',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('tk\\images\\pwrdLogo100.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('tk\\images\\pwrdLogo150.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('tk\\images\\pwrdLogo175.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('tk\\images\\pwrdLogo200.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('tk\\images\\pwrdLogo75.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('tk\\images\\README',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('tk\\images\\tai-ku.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA')])
