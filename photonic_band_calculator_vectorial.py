# photonic_band_calculator_vectorial.py - 完整矢量Maxwell方程实现
import numpy as np
from scipy.linalg import eigh
import time
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
from matplotlib.figure import Figure
import matplotlib.pyplot as plt
import matplotlib
import threading
import os
import multiprocessing as mp
from functools import partial
import sys

# 配置matplotlib
try:
    matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei']
    matplotlib.rcParams['axes.unicode_minus'] = False
    USE_CHINESE = True
except:
    matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial']
    USE_CHINESE = False

# 尝试导入numba
try:
    import numba

    @numba.jit(nopython=True)
    def kappa_corrected(G, ra, epsilon_a, epsilon_b, f):
        """修正的介电常数倒数傅里叶系数计算"""
        G_mag = np.sqrt(G[0]**2 + G[1]**2 + G[2]**2)

        if G_mag < 1e-10:  # G = 0的情况
            # 介电常数倒数的平均值：⟨1/ε⟩ = f/εₐ + (1-f)/εᵦ
            return f/epsilon_a + (1-f)/epsilon_b
        else:
            # G ≠ 0的情况 - 球形散射体的形状因子
            Gra = G_mag * ra
            if Gra < 1e-6:  # 避免数值不稳定，使用泰勒展开
                return f * (1/epsilon_a - 1/epsilon_b) * (1 - (Gra**2)/10 + (Gra**4)/280)
            else:
                # 球形形状因子：S(|G|) = 3[sin(|G|ra) - |G|ra*cos(|G|ra)]/(|G|ra)³
                return 3*f * (1/epsilon_a - 1/epsilon_b) * ((np.sin(Gra) - Gra*np.cos(Gra))/(Gra)**3)

    NUMBA_AVAILABLE = True
except ImportError:
    print("Numba not available, using standard Python implementation")
    NUMBA_AVAILABLE = False

    def kappa_corrected(G, ra, epsilon_a, epsilon_b, f):
        """标准Python实现"""
        G_mag = np.linalg.norm(G)
        if G_mag < 1e-10:
            return f/epsilon_a + (1-f)/epsilon_b
        else:
            Gra = G_mag * ra
            if Gra < 1e-6:
                return f * (1/epsilon_a - 1/epsilon_b) * (1 - (Gra**2)/10 + (Gra**4)/280)
            else:
                return 3*f * (1/epsilon_a - 1/epsilon_b) * ((np.sin(Gra) - Gra*np.cos(Gra))/(Gra)**3)

def generate_fcc_reciprocal_lattice_corrected(n_max, use_cutoff=True):
    """生成修正的FCC结构倒格矢量"""
    G_vectors = []

    # 修正的FCC倒格矢量（对于晶格常数a=1的FCC结构）
    # FCC实格子的倒格子是BCC结构
    # 正确的FCC倒格矢量基矢
    b1 = 2*np.pi*np.array([1, 1, 0])    # 修正
    b2 = 2*np.pi*np.array([1, 0, 1])    # 修正  
    b3 = 2*np.pi*np.array([0, 1, 1])    # 修正

    # 计算截断半径
    if use_cutoff:
        max_G_length = 2*np.pi*n_max*np.sqrt(2)
        cutoff_radius = max_G_length
    else:
        cutoff_radius = float('inf')

    # 生成倒格矢量
    for n1 in range(-n_max, n_max+1):
        for n2 in range(-n_max, n_max+1):
            for n3 in range(-n_max, n_max+1):
                G = n1*b1 + n2*b2 + n3*b3
                G_mag = np.linalg.norm(G)

                if G_mag <= cutoff_radius:
                    G_vectors.append(G)

    # 按照G矢量的长度排序
    G_vectors = np.array(G_vectors)
    if len(G_vectors) > 0:
        G_magnitudes = np.linalg.norm(G_vectors, axis=1)
        sorted_indices = np.argsort(G_magnitudes)
        G_vectors = G_vectors[sorted_indices]

    return G_vectors

def build_vectorial_maxwell_matrix(k_point, G_vectors, ra, epsilon_a, epsilon_b, f):
    """构建完整的矢量Maxwell方程本征值问题矩阵
    
    完整的矢量Maxwell方程：
    ∇ × (1/ε(r)) ∇ × H = (ω/c)² H
    
    在平面波基下，对于每个G矢量，H场有3个分量(Hx, Hy, Hz)
    矩阵大小为 3N × 3N，其中N是G矢量的数量
    
    矩阵元素：
    H_{G,α;G',β} = Σ_γ ε_{αγδ} (k+G)_γ κ(G-G') ε_{δβλ} (k+G')_λ
    
    其中 ε_{αβγ} 是Levi-Civita符号，α,β,γ ∈ {x,y,z}
    """
    n = len(G_vectors)
    # 矩阵大小：3N × 3N (每个G矢量对应3个场分量)
    H = np.zeros((3*n, 3*n), dtype=complex)
    
    # Levi-Civita符号
    def levi_civita(i, j, k):
        """计算Levi-Civita符号"""
        if (i, j, k) in [(0, 1, 2), (1, 2, 0), (2, 0, 1)]:
            return 1
        elif (i, j, k) in [(0, 2, 1), (2, 1, 0), (1, 0, 2)]:
            return -1
        else:
            return 0
    
    for i in range(n):
        for j in range(n):
            Gi = G_vectors[i]
            Gj = G_vectors[j]
            G_diff = Gi - Gj
            
            # 计算介电常数倒数的傅里叶系数
            kappa_val = kappa_corrected(G_diff, ra, epsilon_a, epsilon_b, f)
            
            # 计算 k+G 向量
            k_plus_Gi = k_point + Gi
            k_plus_Gj = k_point + Gj
            
            # 构建3×3子矩阵：H_{G,α;G',β}
            for alpha in range(3):  # H场的分量索引
                for beta in range(3):   # H场的分量索引
                    
                    # 计算矩阵元素：∇ × (1/ε) ∇ × H
                    matrix_element = 0.0
                    
                    # 第一个旋度：∇ × H
                    # (∇ × H)_γ = Σ_δ ε_{γδλ} ∂H_λ/∂x_δ = Σ_δ ε_{γδλ} i(k+G')_δ H_λ
                    # 第二个旋度：∇ × (1/ε(∇ × H))
                    # 结果：Σ_γδλ ε_{αγδ} i(k+G)_γ κ(G-G') ε_{δβλ} i(k+G')_λ H_β
                    # = -Σ_γδλ ε_{αγδ} (k+G)_γ κ(G-G') ε_{δβλ} (k+G')_λ H_β
                    
                    for gamma in range(3):
                        for delta in range(3):
                            for lam in range(3):
                                levi1 = levi_civita(alpha, gamma, delta)
                                levi2 = levi_civita(delta, beta, lam)
                                
                                if levi1 != 0 and levi2 != 0:
                                    matrix_element += (levi1 * k_plus_Gi[gamma] * 
                                                     kappa_val * 
                                                     levi2 * k_plus_Gj[lam])
                    
                    # 矩阵索引
                    row = 3*i + alpha
                    col = 3*j + beta
                    
                    H[row, col] = matrix_element

    return H

def solve_vectorial_eigenvalue_problem(H, num_bands):
    """求解矢量Maxwell方程的特征值问题"""
    try:
        eigenvalues, eigenvectors = eigh(H, driver='evd', overwrite_a=False)
        
        # 过滤掉零频率模式（通常对应于静电场模式）
        # 光子模式的特征值应该为正
        positive_eigenvalues = eigenvalues[eigenvalues > 1e-10]
        
        if len(positive_eigenvalues) < num_bands:
            # 如果正特征值不够，用零填充
            result = np.zeros(num_bands)
            result[:len(positive_eigenvalues)] = positive_eigenvalues[:num_bands]
            return result
        else:
            return positive_eigenvalues[:num_bands]
            
    except Exception as e:
        print(f"矢量特征值计算错误: {str(e)}")
        return np.zeros(num_bands)

def calculate_single_point_vectorial(args):
    """矢量Maxwell方程的单点计算函数"""
    idx, k, G_vectors, ra, epsilon_a, epsilon_b, f, num_bands = args

    try:
        H = build_vectorial_maxwell_matrix(k, G_vectors, ra, epsilon_a, epsilon_b, f)
        eigenvalues = solve_vectorial_eigenvalue_problem(H, num_bands)
        
        # 对于光子晶体，频率 ω = c*sqrt(eigenvalue)
        # 在约化单位中，ω/c = sqrt(eigenvalue) * (2π/a)
        frequencies = np.sqrt(np.abs(eigenvalues))  # 取绝对值避免负值
        
        return (idx, frequencies)
    except Exception as e:
        print(f"计算点 {idx} 出错: {e}")
        return (idx, np.zeros(num_bands))

class PhotonicBandCalculatorVectorial:
    def __init__(self, master):
        self.master = master
        self.master.title("矢量Maxwell方程FCC光子晶体带结构计算器")
        self.master.geometry("1200x800")
        
        # 初始化参数
        self.init_params()
        self.create_gui()
        
        # 计算状态
        self.is_calculating = False
        self.cancel_calculation = False
        self.bands = None
        self.k_path = None
        self.k_labels = []
        self.k_label_positions = []

    def init_params(self):
        """初始化计算参数"""
        # FCC密堆积结构参数
        fcc_close_packed_radius = 1.0 / (2.0 * np.sqrt(2.0))  # ≈ 0.35355
        
        # 基本参数
        self.ra = tk.DoubleVar(value=fcc_close_packed_radius)
        self.epsilon_a = tk.DoubleVar(value=11.56)  # 硅
        self.epsilon_b = tk.DoubleVar(value=1.0)    # 空气
        self.n_max = tk.IntVar(value=3)  # 矢量计算需要更少的平面波以避免矩阵过大
        self.num_bands = tk.IntVar(value=8)  # 减少能带数量
        self.points_per_segment = tk.IntVar(value=15)  # 减少k点数量
        
        # 计算选项
        self.use_parallel = tk.BooleanVar(value=True)
        self.num_processes = tk.IntVar(value=min(4, mp.cpu_count()))
        
        # 单位制
        self.unit_system = tk.StringVar(value="reduced")
        self.sphere_radius_nm = tk.DoubleVar(value=177.0)
        self.calculated_lattice = tk.DoubleVar(value=500.0)
        
        # 修正的FCC高对称点
        a = 2*np.pi
        self.high_sym_points = {
            "Γ": np.array([0.0, 0.0, 0.0]),           # 布里渊区中心
            "X": a*np.array([0.5, 0.0, 0.5]),         # 修正：FCC的X点
            "L": a*np.array([0.5, 0.5, 0.5]),         # L点
            "W": a*np.array([0.5, 0.25, 0.75]),       # 修正：FCC的W点
            "K": a*np.array([0.375, 0.375, 0.75]),    # 修正：FCC的K点
            "U": a*np.array([0.625, 0.25, 0.625])     # 修正：FCC的U点
        }
        
        # 标准FCC带结构路径
        self.default_path = ["L", "Γ", "X", "U", "K", "Γ"]
        self.path_points = self.default_path.copy()

    def create_gui(self):
        """创建图形界面"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.master)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧参数面板
        self.left_frame = ttk.Frame(self.main_frame)
        self.left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)
        
        # 参数设置框架
        self.params_frame = ttk.LabelFrame(self.left_frame, text="参数设置")
        self.params_frame.pack(side=tk.TOP, fill=tk.BOTH, padx=5, pady=5)
        
        # 基本参数
        row = 0
        ttk.Label(self.params_frame, text="球半径 (ra):").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.ra, width=12).grid(row=row, column=1, padx=5, pady=2)
        
        row += 1
        ttk.Label(self.params_frame, text="球介电常数 (εa):").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.epsilon_a, width=12).grid(row=row, column=1, padx=5, pady=2)
        
        row += 1
        ttk.Label(self.params_frame, text="背景介电常数 (εb):").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.epsilon_b, width=12).grid(row=row, column=1, padx=5, pady=2)
        
        row += 1
        ttk.Label(self.params_frame, text="平面波数量 (n_max):").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Spinbox(self.params_frame, textvariable=self.n_max, from_=1, to=5, width=10).grid(row=row, column=1, padx=5, pady=2)
        
        row += 1
        ttk.Label(self.params_frame, text="能带数量:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Spinbox(self.params_frame, textvariable=self.num_bands, from_=1, to=15, width=10).grid(row=row, column=1, padx=5, pady=2)
        
        row += 1
        ttk.Label(self.params_frame, text="k点密度:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Spinbox(self.params_frame, textvariable=self.points_per_segment, from_=5, to=30, width=10).grid(row=row, column=1, padx=5, pady=2)
        
        # 矢量计算说明
        row += 1
        info_label = ttk.Label(self.params_frame, text="注意：矢量计算需要更多内存\n建议n_max≤4, 能带数≤10", 
                              font=('Arial', 8), foreground='red')
        info_label.grid(row=row, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        # 计算选项
        row += 1
        ttk.Separator(self.params_frame, orient='horizontal').grid(row=row, column=0, columnspan=2, sticky='ew', pady=10)
        
        row += 1
        ttk.Label(self.params_frame, text="计算选项:", font=('Arial', 10, 'bold')).grid(row=row, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        row += 1
        ttk.Checkbutton(self.params_frame, text="并行计算", variable=self.use_parallel).grid(row=row, column=0, columnspan=2, sticky=tk.W, padx=5, pady=2)
        
        row += 1
        ttk.Label(self.params_frame, text="进程数:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Spinbox(self.params_frame, textvariable=self.num_processes, from_=1, to=mp.cpu_count(), width=10).grid(row=row, column=1, padx=5, pady=2)
        
        # 单位制选择
        row += 1
        ttk.Separator(self.params_frame, orient='horizontal').grid(row=row, column=0, columnspan=2, sticky='ew', pady=10)
        
        row += 1
        ttk.Label(self.params_frame, text="单位制:", font=('Arial', 10, 'bold')).grid(row=row, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        row += 1
        unit_frame = ttk.Frame(self.params_frame)
        unit_frame.grid(row=row, column=0, columnspan=2, sticky=tk.W, padx=5, pady=2)
        ttk.Radiobutton(unit_frame, text="约化单位 (ω/c)", variable=self.unit_system, value="reduced").pack(side=tk.LEFT)
        ttk.Radiobutton(unit_frame, text="物理单位 (THz)", variable=self.unit_system, value="physical").pack(side=tk.LEFT)
        
        # 物理单位参数
        row += 1
        self.physical_frame = ttk.Frame(self.params_frame)
        self.physical_frame.grid(row=row, column=0, columnspan=2, sticky=tk.W+tk.E, padx=5, pady=2)
        
        sphere_frame = ttk.Frame(self.physical_frame)
        sphere_frame.pack(fill=tk.X, pady=2)
        ttk.Label(sphere_frame, text="球半径 (nm):").pack(side=tk.LEFT)
        ttk.Entry(sphere_frame, textvariable=self.sphere_radius_nm, width=8).pack(side=tk.LEFT, padx=(5, 0))
        ttk.Button(sphere_frame, text="计算晶格", command=self.calculate_lattice_from_sphere).pack(side=tk.LEFT, padx=(5, 0))
        
        lattice_frame = ttk.Frame(self.physical_frame)
        lattice_frame.pack(fill=tk.X, pady=2)
        ttk.Label(lattice_frame, text="晶格常数 (nm):").pack(side=tk.LEFT)
        self.lattice_display = ttk.Label(lattice_frame, text="500.0", relief=tk.SUNKEN, width=10)
        self.lattice_display.pack(side=tk.LEFT, padx=(5, 0))
        
        # 路径信息
        row += 1
        ttk.Separator(self.params_frame, orient='horizontal').grid(row=row, column=0, columnspan=2, sticky='ew', pady=10)
        
        row += 1
        ttk.Label(self.params_frame, text="k路径:", font=('Arial', 10, 'bold')).grid(row=row, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        row += 1
        path_info = " → ".join(self.path_points)
        ttk.Label(self.params_frame, text=path_info, wraplength=200).grid(row=row, column=0, columnspan=2, sticky=tk.W, padx=5, pady=2)
        
        # 按钮
        row += 1
        button_frame = ttk.Frame(self.params_frame)
        button_frame.grid(row=row, column=0, columnspan=2, pady=20)
        
        self.calc_button = ttk.Button(button_frame, text="计算带结构", command=self.start_calculation)
        self.calc_button.pack(side=tk.TOP, fill=tk.X, pady=2)
        
        self.cancel_button = ttk.Button(button_frame, text="取消计算", command=self.cancel_calculation_command, state=tk.DISABLED)
        self.cancel_button.pack(side=tk.TOP, fill=tk.X, pady=2)
        
        ttk.Button(button_frame, text="保存结果", command=self.save_results).pack(side=tk.TOP, fill=tk.X, pady=2)
        
        # 右侧绘图区域
        self.results_frame = ttk.LabelFrame(self.main_frame, text="带结构图")
        self.results_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建matplotlib图形
        self.fig = Figure(figsize=(8, 6), dpi=100)
        self.ax = self.fig.add_subplot(111)
        self.ax.set_xlabel('Wave Vector k')
        self.ax.set_ylabel('Frequency ω/c (in units of 2π/a)')
        self.ax.set_title('矢量Maxwell方程FCC光子晶体带结构\n完整的电磁场矢量计算')
        self.ax.grid(True, linestyle='--', alpha=0.7)
        
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.results_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 状态栏
        self.status_frame = ttk.Frame(self.master)
        self.status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_var = tk.StringVar()
        self.status_var.set("准备就绪 - 使用完整矢量Maxwell方程")
        self.status_label = ttk.Label(self.status_frame, textvariable=self.status_var)
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=2)
        
        self.progress_var = tk.DoubleVar()
        self.progress = ttk.Progressbar(self.status_frame, variable=self.progress_var, mode='determinate')
        self.progress.pack(side=tk.RIGHT, padx=5, pady=2)
        
        # 初始化单位制显示
        self.on_unit_change()

    def on_unit_change(self):
        """单位制切换回调"""
        if self.unit_system.get() == "physical":
            self.physical_frame.pack(fill=tk.X, pady=2)
            self.calculate_lattice_from_sphere()
        else:
            self.physical_frame.pack_forget()

    def calculate_lattice_from_sphere(self):
        """从球半径计算晶格常数"""
        try:
            sphere_radius_nm = self.sphere_radius_nm.get()
            # FCC密堆积：ra = a/(2√2)，所以 a = ra × 2√2
            lattice_constant_nm = sphere_radius_nm * 2.0 * np.sqrt(2.0)
            self.calculated_lattice.set(lattice_constant_nm)
            self.lattice_display.config(text=f"{lattice_constant_nm:.1f}")
            
            # 更新约化半径
            reduced_radius = sphere_radius_nm / lattice_constant_nm
            self.ra.set(reduced_radius)
            
        except Exception as e:
            print(f"计算晶格常数出错: {e}")
            self.lattice_display.config(text="Error")

    def convert_to_physical_units(self, frequencies_reduced):
        """将约化频率转换为物理单位"""
        if self.unit_system.get() == "physical":
            c = 2.998e8  # 光速 (m/s)
            a = self.calculated_lattice.get() * 1e-9  # 晶格常数 (m)
            # 转换公式：f_THz = (ω/c) * (c/a) / (2π) / 1e12
            frequencies_thz = frequencies_reduced * c / a / (2 * np.pi) / 1e12
            return frequencies_thz
        else:
            return frequencies_reduced

    def get_frequency_unit_label(self):
        """获取频率单位标签"""
        if self.unit_system.get() == "physical":
            return "频率 (THz)"
        else:
            return "频率 ω/c (单位: 2π/a)"

    def start_calculation(self):
        """开始计算"""
        if self.is_calculating:
            messagebox.showinfo("计算中", "计算正在进行，请等待...")
            return
        
        # 检查参数合理性
        n_max = self.n_max.get()
        num_bands = self.num_bands.get()
        
        # 估算矩阵大小
        estimated_G_vectors = (2*n_max + 1)**3
        matrix_size = 3 * estimated_G_vectors
        memory_estimate_gb = (matrix_size**2 * 16) / (1024**3)  # 复数矩阵，16字节每元素
        
        if memory_estimate_gb > 2.0:
            if not messagebox.askyesno("内存警告", 
                f"估计矩阵大小：{matrix_size}×{matrix_size}\n"
                f"预计内存使用：{memory_estimate_gb:.1f} GB\n"
                f"建议减少n_max或能带数量。\n"
                f"是否继续计算？"):
                return
        
        self.is_calculating = True
        self.cancel_calculation = False
        self.status_var.set("开始矢量Maxwell方程计算...")
        self.progress_var.set(0)
        
        self.calc_button.configure(state=tk.DISABLED)
        self.cancel_button.configure(state=tk.NORMAL)
        
        # 在新线程中启动计算
        self.calc_thread = threading.Thread(target=self.calculate_band_structure)
        self.calc_thread.daemon = True
        self.calc_thread.start()

    def cancel_calculation_command(self):
        """取消计算"""
        if self.is_calculating:
            self.cancel_calculation = True
            self.status_var.set("正在取消计算...")

    def calculate_band_structure(self):
        """计算带结构主函数"""
        try:
            # 获取参数
            ra = self.ra.get()
            epsilon_a = self.epsilon_a.get()
            epsilon_b = self.epsilon_b.get()
            n_max = self.n_max.get()
            num_bands = self.num_bands.get()
            points_per_segment = self.points_per_segment.get()
            
            # 计算体积分数
            f = 4*np.pi*(ra**3)/3
            
            # 生成修正的倒格矢量
            G_vectors = generate_fcc_reciprocal_lattice_corrected(n_max, use_cutoff=True)
            self.update_status(f"使用 {len(G_vectors)} 个修正的平面波，矩阵大小：{3*len(G_vectors)}×{3*len(G_vectors)}")
            
            if self.cancel_calculation:
                self.cleanup_after_calculation("计算已取消")
                return
            
            # 构建k路径
            self.k_path, self.k_labels, self.k_label_positions = self.build_k_path(points_per_segment)
            total_points = len(self.k_path)
            self.update_status(f"将计算 {total_points} 个k点（矢量Maxwell方程）")
            
            if self.cancel_calculation:
                self.cleanup_after_calculation("计算已取消")
                return
            
            # 计算带结构
            if self.use_parallel.get():
                self.bands = self.calculate_bands_parallel(self.k_path, G_vectors, ra, epsilon_a, epsilon_b, f, num_bands)
            else:
                self.bands = self.calculate_bands_serial(self.k_path, G_vectors, ra, epsilon_a, epsilon_b, f, num_bands)
            
            if self.cancel_calculation:
                self.cleanup_after_calculation("计算已取消")
                return
            
            # 在主线程中更新图形
            self.master.after(0, self.update_plot)
            
        except Exception as e:
            self.update_status(f"计算出错: {str(e)}")
            self.cleanup_after_calculation()
            messagebox.showerror("计算错误", str(e))

    def build_k_path(self, points_per_segment):
        """构建k路径"""
        k_path = []
        k_labels = []
        k_label_positions = []
        
        current_position = 0
        
        for i in range(len(self.path_points) - 1):
            start_label = self.path_points[i]
            end_label = self.path_points[i+1]
            
            start_k = self.high_sym_points[start_label]
            end_k = self.high_sym_points[end_label]
            
            for j in range(points_per_segment):
                if j == 0 and i == 0:
                    # 第一个点
                    k_path.append(start_k)
                    k_labels.append(start_label)
                    k_label_positions.append(current_position)
                    current_position += 1
                elif j == 0:
                    # 其他段的起点已经添加过
                    continue
                elif j == points_per_segment - 1:
                    # 终点
                    k_path.append(end_k)
                    k_labels.append(end_label)
                    k_label_positions.append(current_position)
                    current_position += 1
                else:
                    # 中间点
                    t = j / (points_per_segment - 1)
                    k = (1-t)*start_k + t*end_k
                    k_path.append(k)
                    current_position += 1
        
        return np.array(k_path), k_labels, k_label_positions

    def calculate_bands_serial(self, k_path, G_vectors, ra, epsilon_a, epsilon_b, f, num_bands):
        """串行计算矢量带结构"""
        bands = []
        total_points = len(k_path)
        
        for i, k in enumerate(k_path):
            if self.cancel_calculation:
                return np.zeros((len(k_path), num_bands))
            
            try:
                H = build_vectorial_maxwell_matrix(k, G_vectors, ra, epsilon_a, epsilon_b, f)
                eigenvalues = solve_vectorial_eigenvalue_problem(H, num_bands)
                frequencies = np.sqrt(np.abs(eigenvalues))
                bands.append(frequencies)
                
            except Exception as e:
                self.update_status(f"计算点 {i} 出错: {e}")
                bands.append(np.zeros(num_bands))
            
            # 更新进度
            progress = (i+1) / total_points * 100
            self.master.after(0, lambda p=progress: self.progress_var.set(p))
            
            if (i+1) % 3 == 0:  # 更频繁的状态更新
                self.update_status(f"矢量计算已完成 {i+1}/{total_points} 点 ({progress:.1f}%)")
        
        return np.array(bands)

    def calculate_bands_parallel(self, k_path, G_vectors, ra, epsilon_a, epsilon_b, f, num_bands):
        """并行计算矢量带结构"""
        total_points = len(k_path)
        num_processes = min(self.num_processes.get(), mp.cpu_count())
        
        self.update_status(f"使用 {num_processes} 个进程并行计算矢量Maxwell方程")
        
        # 创建进程池
        pool = mp.Pool(processes=num_processes)
        
        # 设置任务列表
        tasks = []
        for i, k in enumerate(k_path):
            tasks.append((i, k, G_vectors, ra, epsilon_a, epsilon_b, f, num_bands))
        
        # 启动异步计算
        results_async = [pool.apply_async(calculate_single_point_vectorial, (task,)) for task in tasks]
        
        # 等待结果并更新进度
        results = [None] * total_points
        for i, res_async in enumerate(results_async):
            if self.cancel_calculation:
                pool.terminate()
                pool.join()
                return np.zeros((len(k_path), num_bands))
            
            try:
                idx, frequencies = res_async.get(timeout=600)  # 增加超时时间
                results[idx] = frequencies
            except Exception as e:
                self.update_status(f"计算点 {i} 出错: {e}")
                results[i] = np.zeros(num_bands)
            
            # 更新进度
            progress = (i+1) / total_points * 100
            self.master.after(0, lambda p=progress: self.progress_var.set(p))
            
            if (i+1) % 3 == 0:
                self.update_status(f"矢量并行计算已完成 {i+1}/{total_points} 点 ({progress:.1f}%)")
        
        pool.close()
        pool.join()
        
        return np.array(results)

    def update_status(self, message):
        """更新状态"""
        self.master.after(0, lambda: self.status_var.set(message))

    def cleanup_after_calculation(self, status_message="计算完成"):
        """清理计算后的状态"""
        self.is_calculating = False
        self.cancel_calculation = False
        self.update_status(status_message)
        
        self.master.after(0, lambda: self.calc_button.configure(state=tk.NORMAL))
        self.master.after(0, lambda: self.cancel_button.configure(state=tk.DISABLED))

    def update_plot(self):
        """更新绘图"""
        try:
            self.ax.clear()
            
            # 转换为选定的单位制
            display_bands = self.convert_to_physical_units(self.bands)
            
            # 绘制能带
            colors = ['blue', 'red', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']
            for i in range(display_bands.shape[1]):
                color = colors[i % len(colors)]
                self.ax.plot(range(len(self.k_path)), display_bands[:, i],
                           color=color, linewidth=1.5, label=f'Band {i+1}')
            
            # 设置x轴标签
            self.ax.set_xticks(self.k_label_positions)
            self.ax.set_xticklabels(self.k_labels, fontsize=10)
            
            # 在高对称点处绘制垂直线
            for pos in self.k_label_positions:
                self.ax.axvline(x=pos, color='k', linestyle='-', alpha=0.3, linewidth=0.8)
            
            # 分析带隙
            self.analyze_bandgaps(display_bands)
            
            # 设置标题和标签
            self.ax.set_xlabel('Wave Vector k', fontsize=12)
            self.ax.set_ylabel(self.get_frequency_unit_label(), fontsize=12)
            
            # 计算体积分数
            f = 4*np.pi*(self.ra.get()**3)/3
            title = (f'矢量Maxwell方程FCC光子晶体带结构\n'
                    f'路径: {" → ".join(self.path_points)}\n'
                    f'εₐ={self.epsilon_a.get():.1f}, εᵦ={self.epsilon_b.get():.1f}, '
                    f'rₐ={self.ra.get():.5f}, f={f:.3f}')
            self.ax.set_title(title, fontsize=10)
            
            self.ax.grid(True, linestyle='--', alpha=0.7)
            
            # 添加图例
            if display_bands.shape[1] <= 8:
                self.ax.legend(loc='upper right', fontsize=8)
            
            # 更新图形
            self.canvas.draw()
            
            # 更新状态
            self.cleanup_after_calculation("矢量Maxwell方程计算完成")
            self.master.after(0, lambda: self.progress_var.set(100))
            
        except Exception as e:
            self.update_status(f"绘图出错: {str(e)}")
            self.cleanup_after_calculation("绘图出错")
            messagebox.showerror("绘图错误", str(e))

    def analyze_bandgaps(self, bands):
        """分析带隙"""
        try:
            # 跳过零频率模式
            start_band = 1 if bands[0, 0] < 1e-6 else 0
            
            # 寻找完整带隙
            complete_bandgaps = []
            
            for i in range(start_band, bands.shape[1] - 1):
                upper_band = bands[:, i+1]
                lower_band = bands[:, i]
                
                lower_max = np.max(lower_band)
                upper_min = np.min(upper_band)
                
                if upper_min > lower_max:
                    gap_size = upper_min - lower_max
                    gap_center = (upper_min + lower_max) / 2
                    relative_gap = gap_size / gap_center if gap_center > 0 else 0
                    
                    complete_bandgaps.append({
                        'lower_band': i,
                        'upper_band': i+1,
                        'gap_size': gap_size,
                        'gap_center': gap_center,
                        'relative_gap': relative_gap
                    })
            
            # 标记显著的带隙
            gap_colors = ['yellow', 'lightgreen', 'lightblue', 'lightcoral']
            
            for idx, gap in enumerate(complete_bandgaps):
                if gap['relative_gap'] > 0.01:  # 1%以上的相对带隙
                    color = gap_colors[idx % len(gap_colors)]
                    self.ax.axhspan(gap['lower_max'], gap['upper_min'],
                                   alpha=0.3, color=color,
                                   label=f'带隙 {gap["lower_band"]+1}-{gap["upper_band"]+1}')
                    
                    # 添加带隙标注
                    text_x = len(self.k_path) * (0.05 + idx * 0.15)
                    self.ax.text(text_x, gap['gap_center'],
                               f'Δω/ω₀ = {gap["relative_gap"]:.1%}',
                               fontsize=8, ha='center',
                               bbox=dict(boxstyle="round,pad=0.3",
                                       facecolor=color, alpha=0.8))
            
            # 更新状态信息
            if complete_bandgaps:
                gap_info = f"矢量计算发现 {len(complete_bandgaps)} 个完整带隙"
                self.update_status(gap_info)
            else:
                self.update_status("矢量计算未发现显著的完整带隙")
                
        except Exception as e:
            print(f"带隙分析出错: {e}")

    def save_results(self):
        """保存结果"""
        if self.bands is None:
            messagebox.showinfo("保存结果", "请先计算带结构!")
            return
        
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".png",
                filetypes=[("PNG图像", "*.png"), ("PDF文档", "*.pdf"), ("所有文件", "*.*")],
                title="保存带结构图"
            )
            if filename:
                self.fig.savefig(filename, dpi=300, bbox_inches='tight')
                self.status_var.set(f"图像已保存至 {os.path.basename(filename)}")
                
                # 保存数据
                if messagebox.askyesno("保存数据", "是否也保存带结构数据?"):
                    data_filename = os.path.splitext(filename)[0] + ".npz"
                    np.savez(data_filename,
                             bands=self.bands,
                             k_path=self.k_path,
                             k_labels=np.array(self.k_labels, dtype=object),
                             k_label_positions=np.array(self.k_label_positions),
                             method="vectorial_maxwell")
                    self.status_var.set(f"矢量Maxwell方程结果已保存")
        except Exception as e:
            messagebox.showerror("保存错误", str(e))

def main():
    root = tk.Tk()
    app = PhotonicBandCalculatorVectorial(root)
    root.mainloop()

if __name__ == "__main__":
    if sys.platform.startswith('win'):
        mp.set_start_method('spawn', force=True)
        mp.freeze_support()
    main()