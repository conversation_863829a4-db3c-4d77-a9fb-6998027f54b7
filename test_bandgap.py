#!/usr/bin/env python3
"""
测试脚本：验证FCC光子晶体的带隙计算
"""

import numpy as np
import matplotlib.pyplot as plt
from photonic_band_calculator import (
    generate_fcc_reciprocal_lattice, 
    build_matrix, 
    solve_eigenvalue_problem,
    kappa_numba
)

def calculate_band_structure_path():
    """计算完整的带结构路径"""
    print("=== 计算FCC光子晶体带结构 ===")
    
    # 优化的参数设置
    ra = 0.48  # 接近密堆积极限
    epsilon_a = 11.56  # 硅
    epsilon_b = 1.0  # 空气
    f = 4*np.pi*(ra**3)/3
    n_max = 3
    num_bands = 8
    
    print(f"参数: ra={ra}, εa={epsilon_a}, εb={epsilon_b}, f={f:.3f}")
    
    # 生成倒格矢量
    G_vectors = generate_fcc_reciprocal_lattice(n_max, use_cutoff=True)
    print(f"使用 {len(G_vectors)} 个平面波")
    
    # 定义高对称点路径
    a = 2*np.pi
    high_sym_points = {
        "Γ": np.array([0.0, 0.0, 0.0]),
        "X": a*np.array([1.0, 0.0, 0.0]),
        "L": a*np.array([0.5, 0.5, 0.5]),
        "W": a*np.array([1.0, 0.5, 0.0]),
    }
    
    # 构建k路径: Γ-X-W-L-Γ
    path_labels = ["Γ", "X", "W", "L", "Γ"]
    points_per_segment = 20
    
    k_path = []
    k_positions = []
    k_labels = []
    k_label_positions = []
    
    current_pos = 0
    
    for i in range(len(path_labels) - 1):
        start_point = high_sym_points[path_labels[i]]
        end_point = high_sym_points[path_labels[i+1]]
        
        for j in range(points_per_segment):
            if j == 0 and i == 0:
                # 第一个点
                k_path.append(start_point)
                k_labels.append(path_labels[i])
                k_label_positions.append(current_pos)
                current_pos += 1
            elif j == 0:
                # 跳过重复的起点
                continue
            elif j == points_per_segment - 1:
                # 终点
                k_path.append(end_point)
                k_labels.append(path_labels[i+1])
                k_label_positions.append(current_pos)
                current_pos += 1
            else:
                # 中间点
                t = j / (points_per_segment - 1)
                k = (1-t)*start_point + t*end_point
                k_path.append(k)
                current_pos += 1
    
    k_path = np.array(k_path)
    print(f"k路径包含 {len(k_path)} 个点")
    
    # 计算带结构
    bands = []
    print("计算带结构...")
    
    for i, k in enumerate(k_path):
        if i % 10 == 0:
            print(f"进度: {i+1}/{len(k_path)}")
        
        # 构建矩阵
        H = build_matrix(k, G_vectors, ra, epsilon_a, epsilon_b, f)
        
        # 求解特征值
        eigenvalues = solve_eigenvalue_problem(H, num_bands, "eigh")
        frequencies = np.sqrt(eigenvalues)
        bands.append(frequencies)
    
    bands = np.array(bands)
    
    # 分析带隙
    analyze_complete_bandgaps(bands, k_labels, k_label_positions)
    
    # 绘制带结构
    plot_band_structure(bands, k_labels, k_label_positions, ra, epsilon_a, epsilon_b)
    
    return bands, k_path, k_labels, k_label_positions

def analyze_complete_bandgaps(bands, k_labels, k_label_positions):
    """分析完整带隙"""
    print("\n=== 带隙分析 ===")
    
    # 跳过零频率模式
    start_band = 1 if bands[0, 0] < 1e-6 else 0
    
    complete_gaps = []
    
    for i in range(start_band, bands.shape[1] - 1):
        lower_band = bands[:, i]
        upper_band = bands[:, i+1]
        
        lower_max = np.max(lower_band)
        upper_min = np.min(upper_band)
        
        if upper_min > lower_max:
            gap_size = upper_min - lower_max
            gap_center = (upper_min + lower_max) / 2
            relative_gap = gap_size / gap_center if gap_center > 0 else 0
            
            complete_gaps.append({
                'bands': (i, i+1),
                'gap_size': gap_size,
                'gap_center': gap_center,
                'relative_gap': relative_gap,
                'lower_max': lower_max,
                'upper_min': upper_min
            })
            
            print(f"完整带隙 {i+1}-{i+2}:")
            print(f"  频率范围: {lower_max:.4f} - {upper_min:.4f}")
            print(f"  带隙大小: {gap_size:.4f}")
            print(f"  相对大小: {relative_gap:.1%}")
    
    if not complete_gaps:
        print("未发现完整带隙")
    
    # 分析高对称点的频率
    print("\n=== 高对称点频率 ===")
    for i, pos in enumerate(k_label_positions):
        if pos < len(bands):
            label = k_labels[i]
            freqs = bands[pos, start_band:start_band+5]  # 显示前5个非零频率
            print(f"{label}点: {freqs}")

def plot_band_structure(bands, k_labels, k_label_positions, ra, epsilon_a, epsilon_b):
    """绘制带结构"""
    plt.figure(figsize=(10, 6))
    
    # 跳过零频率模式
    start_band = 1 if bands[0, 0] < 1e-6 else 0
    
    # 绘制能带
    colors = ['blue', 'red', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']
    for i in range(start_band, bands.shape[1]):
        color = colors[(i-start_band) % len(colors)]
        plt.plot(range(len(bands)), bands[:, i], color=color, linewidth=1.5)
    
    # 设置x轴标签
    plt.xticks(k_label_positions, k_labels)
    
    # 在高对称点处绘制垂直线
    for pos in k_label_positions:
        plt.axvline(x=pos, color='k', linestyle='-', alpha=0.3)
    
    # 标记带隙
    for i in range(start_band, bands.shape[1] - 1):
        lower_band = bands[:, i]
        upper_band = bands[:, i+1]
        
        lower_max = np.max(lower_band)
        upper_min = np.min(upper_band)
        
        if upper_min > lower_max:
            gap_size = upper_min - lower_max
            relative_gap = gap_size / ((upper_min + lower_max)/2)
            
            if relative_gap > 0.01:  # 显示1%以上的带隙
                plt.axhspan(lower_max, upper_min, alpha=0.3, color='yellow')
                plt.text(len(bands)*0.02, (lower_max + upper_min)/2, 
                        f'{relative_gap:.1%}', fontsize=8,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
    
    plt.xlabel('Wave Vector k')
    plt.ylabel('Frequency ω/c')
    plt.title(f'FCC光子晶体带结构 (εₐ={epsilon_a}, εᵦ=1.0, rₐ={ra})')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('fcc_band_structure.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    print("FCC光子晶体带隙分析")
    print("=" * 50)
    
    try:
        bands, k_path, k_labels, k_label_positions = calculate_band_structure_path()
        print("\n计算完成！带结构图已保存为 'fcc_band_structure.png'")
        
    except Exception as e:
        print(f"计算过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
