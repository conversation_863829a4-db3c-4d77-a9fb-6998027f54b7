#!/usr/bin/env python3
"""
优化脚本：寻找产生最大带隙的FCC光子晶体参数
"""

import numpy as np
import matplotlib.pyplot as plt
from photonic_band_calculator import (
    generate_fcc_reciprocal_lattice, 
    build_matrix, 
    solve_eigenvalue_problem
)

def calculate_bandgap_at_gamma(ra, epsilon_a, epsilon_b=1.0, n_max=3, num_bands=8):
    """计算Γ点的带隙"""
    try:
        f = 4*np.pi*(ra**3)/3
        
        # 生成倒格矢量
        G_vectors = generate_fcc_reciprocal_lattice(n_max, use_cutoff=True)
        
        # 计算Γ点
        k_gamma = np.array([0.0, 0.0, 0.0])
        H = build_matrix(k_gamma, G_vectors, ra, epsilon_a, epsilon_b, f)
        eigenvalues = solve_eigenvalue_problem(H, num_bands, "eigh")
        frequencies = np.sqrt(eigenvalues)
        
        # 跳过零频率模式
        start_idx = 1 if frequencies[0] < 1e-6 else 0
        valid_freqs = frequencies[start_idx:]
        
        # 寻找最大带隙
        max_gap = 0
        max_gap_info = None
        
        for i in range(len(valid_freqs) - 1):
            gap = valid_freqs[i+1] - valid_freqs[i]
            relative_gap = gap / valid_freqs[i] if valid_freqs[i] > 0 else 0
            
            if gap > max_gap:
                max_gap = gap
                max_gap_info = {
                    'gap_size': gap,
                    'relative_gap': relative_gap,
                    'lower_freq': valid_freqs[i],
                    'upper_freq': valid_freqs[i+1],
                    'bands': (i, i+1)
                }
        
        return max_gap_info, frequencies
        
    except Exception as e:
        print(f"计算出错 (ra={ra}, εa={epsilon_a}): {e}")
        return None, None

def scan_parameters():
    """扫描参数空间寻找最佳带隙"""
    print("=== 参数扫描寻找最佳带隙 ===")
    
    # 参数范围
    ra_values = np.linspace(0.1, 0.49, 20)  # 球半径范围
    epsilon_values = [2.0, 4.0, 8.0, 11.56, 16.0, 25.0]  # 不同介电常数
    
    best_gap = 0
    best_params = None
    results = []
    
    total_calculations = len(ra_values) * len(epsilon_values)
    current_calc = 0
    
    for epsilon_a in epsilon_values:
        for ra in ra_values:
            current_calc += 1
            print(f"进度: {current_calc}/{total_calculations} - ra={ra:.3f}, εa={epsilon_a}")
            
            gap_info, frequencies = calculate_bandgap_at_gamma(ra, epsilon_a)
            
            if gap_info and gap_info['relative_gap'] > 0.01:  # 只考虑1%以上的带隙
                results.append({
                    'ra': ra,
                    'epsilon_a': epsilon_a,
                    'gap_info': gap_info,
                    'frequencies': frequencies
                })
                
                if gap_info['relative_gap'] > best_gap:
                    best_gap = gap_info['relative_gap']
                    best_params = {
                        'ra': ra,
                        'epsilon_a': epsilon_a,
                        'gap_info': gap_info
                    }
    
    return results, best_params

def plot_parameter_scan(results):
    """绘制参数扫描结果"""
    if not results:
        print("没有找到显著的带隙")
        return
    
    # 提取数据
    ra_vals = [r['ra'] for r in results]
    epsilon_vals = [r['epsilon_a'] for r in results]
    gap_sizes = [r['gap_info']['relative_gap'] for r in results]
    
    # 创建散点图
    plt.figure(figsize=(12, 8))
    
    # 子图1：ra vs 相对带隙大小
    plt.subplot(2, 2, 1)
    scatter = plt.scatter(ra_vals, gap_sizes, c=epsilon_vals, cmap='viridis', s=50)
    plt.xlabel('球半径 ra')
    plt.ylabel('相对带隙大小')
    plt.title('带隙 vs 球半径')
    plt.colorbar(scatter, label='介电常数 εa')
    plt.grid(True, alpha=0.3)
    
    # 子图2：介电常数 vs 相对带隙大小
    plt.subplot(2, 2, 2)
    scatter2 = plt.scatter(epsilon_vals, gap_sizes, c=ra_vals, cmap='plasma', s=50)
    plt.xlabel('介电常数 εa')
    plt.ylabel('相对带隙大小')
    plt.title('带隙 vs 介电常数')
    plt.colorbar(scatter2, label='球半径 ra')
    plt.grid(True, alpha=0.3)
    
    # 子图3：2D热图
    plt.subplot(2, 2, 3)
    # 创建网格数据
    unique_ra = sorted(list(set(ra_vals)))
    unique_eps = sorted(list(set(epsilon_vals)))
    
    gap_matrix = np.zeros((len(unique_eps), len(unique_ra)))
    
    for result in results:
        i = unique_eps.index(result['epsilon_a'])
        j = unique_ra.index(result['ra'])
        gap_matrix[i, j] = result['gap_info']['relative_gap']
    
    im = plt.imshow(gap_matrix, aspect='auto', origin='lower', cmap='hot')
    plt.xlabel('球半径索引')
    plt.ylabel('介电常数索引')
    plt.title('带隙热图')
    plt.colorbar(im, label='相对带隙大小')
    
    # 子图4：最佳参数的频率谱
    plt.subplot(2, 2, 4)
    if results:
        # 找到最大带隙的结果
        best_result = max(results, key=lambda x: x['gap_info']['relative_gap'])
        freqs = best_result['frequencies']
        
        # 跳过零频率
        start_idx = 1 if freqs[0] < 1e-6 else 0
        valid_freqs = freqs[start_idx:start_idx+8]
        
        plt.plot(range(len(valid_freqs)), valid_freqs, 'bo-', linewidth=2, markersize=8)
        plt.xlabel('能带索引')
        plt.ylabel('频率 ω/c')
        plt.title(f'最佳参数频率谱\nra={best_result["ra"]:.3f}, εa={best_result["epsilon_a"]:.1f}')
        plt.grid(True, alpha=0.3)
        
        # 标记带隙
        gap_info = best_result['gap_info']
        lower_idx = gap_info['bands'][0]
        upper_idx = gap_info['bands'][1]
        plt.axhspan(gap_info['lower_freq'], gap_info['upper_freq'], 
                   alpha=0.3, color='yellow', label=f'带隙: {gap_info["relative_gap"]:.1%}')
        plt.legend()
    
    plt.tight_layout()
    plt.savefig('parameter_scan_results.png', dpi=300, bbox_inches='tight')
    plt.show()

def calculate_optimized_band_structure(best_params):
    """使用最佳参数计算完整带结构"""
    if not best_params:
        print("没有找到最佳参数")
        return
    
    print(f"\n=== 使用最佳参数计算带结构 ===")
    print(f"最佳参数: ra={best_params['ra']:.3f}, εa={best_params['epsilon_a']:.1f}")
    print(f"预期带隙: {best_params['gap_info']['relative_gap']:.1%}")
    
    ra = best_params['ra']
    epsilon_a = best_params['epsilon_a']
    epsilon_b = 1.0
    f = 4*np.pi*(ra**3)/3
    n_max = 4  # 提高精度
    num_bands = 10
    
    # 生成倒格矢量
    G_vectors = generate_fcc_reciprocal_lattice(n_max, use_cutoff=True)
    print(f"使用 {len(G_vectors)} 个平面波")
    
    # 定义k路径
    a = 2*np.pi
    high_sym_points = {
        "Γ": np.array([0.0, 0.0, 0.0]),
        "X": a*np.array([1.0, 0.0, 0.0]),
        "L": a*np.array([0.5, 0.5, 0.5]),
        "W": a*np.array([1.0, 0.5, 0.0]),
    }
    
    path_labels = ["Γ", "X", "W", "L", "Γ"]
    points_per_segment = 25
    
    # 构建k路径
    k_path = []
    k_label_positions = []
    k_labels = []
    current_pos = 0
    
    for i in range(len(path_labels) - 1):
        start_point = high_sym_points[path_labels[i]]
        end_point = high_sym_points[path_labels[i+1]]
        
        for j in range(points_per_segment):
            if j == 0 and i == 0:
                k_path.append(start_point)
                k_labels.append(path_labels[i])
                k_label_positions.append(current_pos)
                current_pos += 1
            elif j == 0:
                continue
            elif j == points_per_segment - 1:
                k_path.append(end_point)
                k_labels.append(path_labels[i+1])
                k_label_positions.append(current_pos)
                current_pos += 1
            else:
                t = j / (points_per_segment - 1)
                k = (1-t)*start_point + t*end_point
                k_path.append(k)
                current_pos += 1
    
    k_path = np.array(k_path)
    
    # 计算带结构
    bands = []
    print("计算优化的带结构...")
    
    for i, k in enumerate(k_path):
        if i % 20 == 0:
            print(f"进度: {i+1}/{len(k_path)}")
        
        H = build_matrix(k, G_vectors, ra, epsilon_a, epsilon_b, f)
        eigenvalues = solve_eigenvalue_problem(H, num_bands, "eigh")
        frequencies = np.sqrt(eigenvalues)
        bands.append(frequencies)
    
    bands = np.array(bands)
    
    # 绘制优化的带结构
    plt.figure(figsize=(10, 6))
    
    start_band = 1 if bands[0, 0] < 1e-6 else 0
    colors = ['blue', 'red', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']
    
    for i in range(start_band, min(bands.shape[1], start_band + 8)):
        color = colors[(i-start_band) % len(colors)]
        plt.plot(range(len(bands)), bands[:, i], color=color, linewidth=1.5)
    
    plt.xticks(k_label_positions, k_labels)
    
    for pos in k_label_positions:
        plt.axvline(x=pos, color='k', linestyle='-', alpha=0.3)
    
    # 分析并标记带隙
    for i in range(start_band, bands.shape[1] - 1):
        lower_band = bands[:, i]
        upper_band = bands[:, i+1]
        
        lower_max = np.max(lower_band)
        upper_min = np.min(upper_band)
        
        if upper_min > lower_max:
            gap_size = upper_min - lower_max
            relative_gap = gap_size / ((upper_min + lower_max)/2)
            
            if relative_gap > 0.01:
                plt.axhspan(lower_max, upper_min, alpha=0.3, color='yellow')
                plt.text(len(bands)*0.02, (lower_max + upper_min)/2, 
                        f'{relative_gap:.1%}', fontsize=8,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
    
    plt.xlabel('Wave Vector k')
    plt.ylabel('Frequency ω/c')
    plt.title(f'优化的FCC光子晶体带结构\nra={ra:.3f}, εa={epsilon_a:.1f}, f={f:.3f}')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('optimized_band_structure.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    print("FCC光子晶体带隙优化")
    print("=" * 50)
    
    try:
        # 参数扫描
        results, best_params = scan_parameters()
        
        print(f"\n扫描完成！找到 {len(results)} 个有带隙的参数组合")
        
        if best_params:
            print(f"\n最佳参数:")
            print(f"  球半径: {best_params['ra']:.3f}")
            print(f"  介电常数: {best_params['epsilon_a']:.1f}")
            print(f"  最大相对带隙: {best_params['gap_info']['relative_gap']:.1%}")
            print(f"  带隙大小: {best_params['gap_info']['gap_size']:.4f}")
        
        # 绘制扫描结果
        plot_parameter_scan(results)
        
        # 计算最佳参数的完整带结构
        calculate_optimized_band_structure(best_params)
        
        print("\n优化完成！结果已保存为图像文件。")
        
    except Exception as e:
        print(f"优化过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
