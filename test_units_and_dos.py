#!/usr/bin/env python3
"""
测试脚本：验证单位制转换和DOS计算功能
"""

import numpy as np
import matplotlib.pyplot as plt
from photonic_band_calculator import (
    generate_fcc_reciprocal_lattice, 
    build_matrix, 
    solve_eigenvalue_problem
)

def test_unit_conversion():
    """测试单位制转换"""
    print("=== 单位制转换测试 ===")
    
    # 测试参数
    frequencies_reduced = np.array([1.0, 2.0, 3.0, 4.0, 5.0])  # 约化频率 ω/c
    lattice_constant_nm = 500.0  # 晶格常数 (nm)
    
    # 物理常数
    c = 2.998e8  # 光速 (m/s)
    a = lattice_constant_nm * 1e-9  # 晶格常数 (m)
    
    # 转换为THz
    frequencies_thz = frequencies_reduced * c / a / (2 * np.pi) / 1e12
    
    print(f"晶格常数: {lattice_constant_nm} nm")
    print(f"约化频率 ω/c: {frequencies_reduced}")
    print(f"物理频率 (THz): {frequencies_thz}")
    print(f"转换因子: {c / a / (2 * np.pi) / 1e12:.3f} THz per (ω/c)")
    
    return frequencies_thz

def test_corrected_path():
    """测试修正的路径: X-U-L-Γ-X-W-K"""
    print("\n=== 修正路径测试 ===")
    
    # 定义高对称点（移除eta，使用Γ）
    a = 2*np.pi
    high_sym_points = {
        "Γ": np.array([0.0, 0.0, 0.0]),
        "X": a*np.array([1.0, 0.0, 0.0]),
        "L": a*np.array([0.5, 0.5, 0.5]),
        "W": a*np.array([1.0, 0.5, 0.0]),
        "K": a*np.array([0.75, 0.75, 0.0]),
        "U": a*np.array([1.0, 0.25, 0.25])
    }
    
    # 修正的路径
    path_labels = ["X", "U", "L", "Γ", "X", "W", "K"]
    
    print("高对称点坐标:")
    for label, point in high_sym_points.items():
        print(f"{label:2s}: ({point[0]:.3f}, {point[1]:.3f}, {point[2]:.3f})")
    
    print(f"\n修正路径: {' → '.join(path_labels)}")
    print("注意: eta点已替换为Γ点（倒空间原点）")
    
    return high_sym_points, path_labels

def calculate_dos_example():
    """计算DOS示例"""
    print("\n=== DOS计算示例 ===")
    
    # FCC密堆积参数
    ra = 1.0 / (2.0 * np.sqrt(2.0))
    epsilon_a = 11.56
    epsilon_b = 1.0
    f = 4*np.pi*(ra**3)/3
    n_max = 3
    num_bands = 6
    
    print(f"参数: ra={ra:.5f}, εa={epsilon_a}, f={f:.4f}")
    
    # 生成倒格矢量
    G_vectors = generate_fcc_reciprocal_lattice(n_max, use_cutoff=True)
    print(f"使用 {len(G_vectors)} 个平面波")
    
    # 定义k路径
    a = 2*np.pi
    high_sym_points = {
        "X": a*np.array([1.0, 0.0, 0.0]),
        "U": a*np.array([1.0, 0.25, 0.25]),
        "L": a*np.array([0.5, 0.5, 0.5]),
        "Γ": np.array([0.0, 0.0, 0.0]),
        "W": a*np.array([1.0, 0.5, 0.0]),
        "K": a*np.array([0.75, 0.75, 0.0])
    }
    
    path_labels = ["X", "U", "L", "Γ", "X", "W", "K"]
    points_per_segment = 10
    
    # 构建k路径
    k_path = []
    k_label_positions = []
    k_labels = []
    current_pos = 0
    
    for i in range(len(path_labels) - 1):
        start_point = high_sym_points[path_labels[i]]
        end_point = high_sym_points[path_labels[i+1]]
        
        for j in range(points_per_segment):
            if j == 0 and i == 0:
                k_path.append(start_point)
                k_labels.append(path_labels[i])
                k_label_positions.append(current_pos)
                current_pos += 1
            elif j == 0:
                continue
            elif j == points_per_segment - 1:
                k_path.append(end_point)
                k_labels.append(path_labels[i+1])
                k_label_positions.append(current_pos)
                current_pos += 1
            else:
                t = j / (points_per_segment - 1)
                k = (1-t)*start_point + t*end_point
                k_path.append(k)
                current_pos += 1
    
    k_path = np.array(k_path)
    print(f"k路径点数: {len(k_path)}")
    
    # 计算带结构
    bands = []
    print("计算带结构...")
    
    for i, k in enumerate(k_path):
        if i % 10 == 0:
            print(f"  进度: {i+1}/{len(k_path)}")
        
        H = build_matrix(k, G_vectors, ra, epsilon_a, epsilon_b, f)
        eigenvalues = solve_eigenvalue_problem(H, num_bands, "eigh")
        frequencies = np.sqrt(eigenvalues)
        bands.append(frequencies)
    
    bands = np.array(bands)
    
    # 计算DOS
    print("\n计算DOS...")
    
    # 跳过零频率模式
    start_band = 1 if bands[0, 0] < 1e-6 else 0
    valid_bands = bands[:, start_band:]
    
    # 展平所有频率值
    all_frequencies = valid_bands.flatten()
    
    # 创建能量网格
    freq_min = np.min(all_frequencies)
    freq_max = np.max(all_frequencies)
    freq_range = freq_max - freq_min
    
    freq_min -= freq_range * 0.1
    freq_max += freq_range * 0.1
    
    energy_grid = np.linspace(freq_min, freq_max, 1000)
    
    # 使用高斯展宽计算DOS
    sigma = freq_range / 100
    dos = np.zeros_like(energy_grid)
    
    for freq in all_frequencies:
        dos += np.exp(-0.5 * ((energy_grid - freq) / sigma) ** 2)
    
    dos /= (sigma * np.sqrt(2 * np.pi))
    dos /= len(k_path)
    
    print(f"DOS计算完成，频率范围: {freq_min:.3f} - {freq_max:.3f}")
    
    # 绘制结果
    plot_bands_and_dos(bands, k_labels, k_label_positions, energy_grid, dos, ra, epsilon_a)
    
    return bands, energy_grid, dos

def plot_bands_and_dos(bands, k_labels, k_label_positions, energy_grid, dos, ra, epsilon_a):
    """绘制带结构和DOS"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6), gridspec_kw={'width_ratios': [3, 1]})
    
    # 绘制带结构
    start_band = 1 if bands[0, 0] < 1e-6 else 0
    colors = ['blue', 'red', 'green', 'orange', 'purple', 'brown']
    
    for i in range(start_band, bands.shape[1]):
        color = colors[(i-start_band) % len(colors)]
        ax1.plot(range(len(bands)), bands[:, i], color=color, linewidth=1.5)
    
    ax1.set_xticks(k_label_positions)
    ax1.set_xticklabels(k_labels)
    
    for pos in k_label_positions:
        ax1.axvline(x=pos, color='k', linestyle='-', alpha=0.3)
    
    ax1.set_xlabel('Wave Vector k')
    ax1.set_ylabel('Frequency ω/c')
    ax1.set_title(f'FCC光子晶体带结构\n路径: X→U→L→Γ→X→W→K\nra={ra:.5f}, εa={epsilon_a}')
    ax1.grid(True, alpha=0.3)
    
    # 绘制DOS
    ax2.plot(dos, energy_grid, 'k-', linewidth=2)
    ax2.set_xlabel('DOS')
    ax2.set_ylabel('Frequency ω/c')
    ax2.set_title('态密度')
    ax2.grid(True, alpha=0.3)
    
    # 设置DOS的y轴范围与带结构一致
    ax2.set_ylim(ax1.get_ylim())
    
    plt.tight_layout()
    plt.savefig('fcc_bands_dos_corrected.png', dpi=300, bbox_inches='tight')
    plt.show()

def test_physical_units():
    """测试物理单位转换"""
    print("\n=== 物理单位转换测试 ===")
    
    # 测试不同晶格常数的转换
    lattice_constants = [300, 500, 800, 1000]  # nm
    reduced_freq = 1.0  # ω/c = 1
    
    c = 2.998e8  # 光速 (m/s)
    
    print("晶格常数 (nm) | 物理频率 (THz) | 对应波长 (nm)")
    print("-" * 50)
    
    for a_nm in lattice_constants:
        a_m = a_nm * 1e-9
        freq_thz = reduced_freq * c / a_m / (2 * np.pi) / 1e12
        wavelength_nm = c / (freq_thz * 1e12) * 1e9
        print(f"{a_nm:12d} | {freq_thz:13.2f} | {wavelength_nm:13.1f}")
    
    print("\n注意: 这些是对应于约化频率 ω/c = 1 的物理频率")

if __name__ == "__main__":
    print("FCC光子晶体单位制和DOS测试")
    print("=" * 50)
    
    try:
        # 测试单位制转换
        test_unit_conversion()
        
        # 测试修正的路径
        test_corrected_path()
        
        # 测试物理单位
        test_physical_units()
        
        # 计算DOS示例
        calculate_dos_example()
        
        print("\n" + "=" * 50)
        print("所有测试完成！")
        print("- 路径已修正为: X→U→L→Γ→X→W→K")
        print("- 支持约化单位和物理单位转换")
        print("- DOS计算功能已实现")
        print("- 结果图已保存为 'fcc_bands_dos_corrected.png'")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
