# photonic_band_calculator.py
import numpy as np
from scipy.linalg import eigh
import time
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
from matplotlib.figure import Figure
import threading
import os
import multiprocessing as mp
from functools import partial
from scipy.optimize import linear_sum_assignment
import sys

# 尝试导入numba，如果不可用则提供替代方案
try:
    import numba

    @numba.jit(nopython=True)
    def kappa_numba(G, ra, epsilon_a, epsilon_b, f):
        """使用Numba加速的kappa计算 - 修正的形状因子"""
        G_mag = np.sqrt(G[0]**2 + G[1]**2 + G[2]**2)

        if G_mag < 1e-10:  # G = 0的情况
            # 修正：应该是介电常数的倒数的加权平均
            return f/epsilon_a + (1-f)/epsilon_b
        else:
            # G ≠ 0的情况 - 球形散射体的形状因子
            Gra = G_mag * ra
            if Gra < 1e-6:  # 避免数值不稳定
                # 使用泰勒展开
                return f * (1/epsilon_a - 1/epsilon_b) * (1 - (Gra**2)/10 + (Gra**4)/280)
            else:
                return 3*f * (1/epsilon_a - 1/epsilon_b) * ((np.sin(Gra) - Gra*np.cos(Gra))/(Gra)**3)

    NUMBA_AVAILABLE = True
except ImportError:
    print("Numba not available, using standard Python implementation")
    NUMBA_AVAILABLE = False

    def kappa_numba(G, ra, epsilon_a, epsilon_b, f):
        """标准Python实现的kappa计算 - 修正的形状因子"""
        G_mag = np.linalg.norm(G)

        if G_mag < 1e-10:  # G = 0的情况
            return f/epsilon_a + (1-f)/epsilon_b
        else:
            # G ≠ 0的情况 - 球形散射体的形状因子
            Gra = G_mag * ra
            if Gra < 1e-6:  # 避免数值不稳定
                # 使用泰勒展开
                return f * (1/epsilon_a - 1/epsilon_b) * (1 - (Gra**2)/10 + (Gra**4)/280)
            else:
                return 3*f * (1/epsilon_a - 1/epsilon_b) * ((np.sin(Gra) - Gra*np.cos(Gra))/(Gra)**3)

# 尝试导入scipy.sparse，如果不可用则提供替代方案
try:
    from scipy.sparse import lil_matrix, csr_matrix
    from scipy.sparse.linalg import eigsh, lobpcg
    SPARSE_AVAILABLE = True
except ImportError:
    print("Sparse matrix support not available")
    SPARSE_AVAILABLE = False

# 尝试导入pydavidson，如果不可用则提供替代方案
try:
    import pydavidson
    DAVIDSON_AVAILABLE = True
except ImportError:
    print("Davidson solver not available")
    DAVIDSON_AVAILABLE = False

# 将矩阵构建函数移到类外部
def build_matrix(k_point, G_vectors, ra, epsilon_a, epsilon_b, f):
    """构建本征值问题的矩阵 - Maxwell方程的本征值形式"""
    n = len(G_vectors)
    H = np.zeros((n, n), dtype=complex)

    for i in range(n):
        for j in range(n):
            Gi = G_vectors[i]
            Gj = G_vectors[j]
            G_diff = Gi - Gj

            # 计算介电常数的倒数的傅里叶系数
            kappa_val = kappa_numba(G_diff, ra, epsilon_a, epsilon_b, f)

            # Maxwell方程的矩阵元素: (k+Gi) · κ(Gi-Gj) · (k+Gj)
            k_plus_Gi = k_point + Gi
            k_plus_Gj = k_point + Gj

            # 对于标量情况，矩阵元素为
            H[i, j] = kappa_val * np.dot(k_plus_Gi, k_plus_Gj)

    return H

def build_matrix_sparse(k_point, G_vectors, ra, epsilon_a, epsilon_b, f):
    """构建本征值问题的稀疏矩阵"""
    if not SPARSE_AVAILABLE:
        return build_matrix(k_point, G_vectors, ra, epsilon_a, epsilon_b, f)

    n = len(G_vectors)
    H = lil_matrix((n, n), dtype=complex)

    for i in range(n):
        for j in range(n):
            Gi = G_vectors[i]
            Gj = G_vectors[j]
            G_diff = Gi - Gj

            # 计算介电常数的倒数的傅里叶系数
            kappa_val = kappa_numba(G_diff, ra, epsilon_a, epsilon_b, f)

            # Maxwell方程的矩阵元素
            k_plus_Gi = k_point + Gi
            k_plus_Gj = k_point + Gj

            matrix_element = kappa_val * np.dot(k_plus_Gi, k_plus_Gj)

            # 只保留较大的矩阵元素以提高稀疏性
            if abs(matrix_element) > 1e-12:
                H[i, j] = matrix_element

    return H.tocsr()  # 转换为CSR格式用于计算

def solve_eigenvalue_problem(H, num_bands, diag_method="eigh", eigsh_tol=1e-6, eigsh_maxiter=1000, idx=None):
    """使用选定的方法求解特征值问题"""
    try:
        if diag_method == "eigh":
            # 传统的密集矩阵求解器 - 使用更高精度的设置
            if not isinstance(H, np.ndarray):
                H = H.toarray()
            eigenvalues, _ = eigh(H, driver='evd', overwrite_a=False)
            return np.sort(eigenvalues[:num_bands])

        elif diag_method == "eigsh":
            # 稀疏矩阵Lanczos方法
            if not isinstance(H, csr_matrix):
                if isinstance(H, np.ndarray):
                    H = csr_matrix(H)
                else:
                    H = H.tocsr()

            eigenvalues, _ = eigsh(H, k=num_bands, which='SM',
                                  tol=eigsh_tol,
                                  maxiter=eigsh_maxiter)
            return np.sort(eigenvalues)

        elif diag_method == "lobpcg":
            # LOBPCG方法
            if not SPARSE_AVAILABLE:
                raise ImportError("LOBPCG需要scipy.sparse支持")

            if isinstance(H, np.ndarray):
                matrix = H
            else:
                matrix = H.toarray()

            # 创建初始猜测向量
            X = np.random.rand(matrix.shape[0], num_bands)
            eigenvalues, _ = lobpcg(matrix, X, largest=False,
                                   tol=eigsh_tol,
                                   maxiter=eigsh_maxiter)
            return np.sort(eigenvalues)

        elif diag_method == "arpack":
            # ARPACK方法
            if not SPARSE_AVAILABLE:
                raise ImportError("ARPACK需要scipy.sparse支持")

            if not isinstance(H, csr_matrix):
                if isinstance(H, np.ndarray):
                    H = csr_matrix(H)
                else:
                    H = H.tocsr()

            # 使用shift-invert模式提高低能量特征值的准确度
            eigenvalues, _ = eigsh(H, k=num_bands, which='SM',
                                  sigma=0.0, mode='normal',
                                  tol=eigsh_tol,
                                  maxiter=eigsh_maxiter)
            return np.sort(eigenvalues)

        elif diag_method == "davidson":
            # Davidson方法
            if not DAVIDSON_AVAILABLE:
                raise ImportError("Davidson方法需要pydavidson包")

            from pydavidson import davidson
            if isinstance(H, np.ndarray):
                matrix = H
            else:
                matrix = H.toarray()

            eigenvalues, _ = davidson(matrix, k=num_bands,
                                     tol=eigsh_tol,
                                     max_iter=eigsh_maxiter)
            return np.sort(eigenvalues)

        else:
            # 默认回退到eigh
            if not isinstance(H, np.ndarray):
                H = H.toarray()
            eigenvalues, _ = eigh(H)
            return np.sort(eigenvalues[:num_bands])

    except Exception as e:
        print(f"Eigenvalue calculation error: {str(e)}")
        # 返回零值作为fallback
        return np.zeros(num_bands)

# 将单点计算函数移到类外部
def calculate_single_point(args):
    """计算单个k点的能带"""
    idx, k, G_vectors, ra, epsilon_a, epsilon_b, f, num_bands, use_sparse, diag_method, eigsh_tol, eigsh_maxiter = args

    try:
        if use_sparse and SPARSE_AVAILABLE:
            H = build_matrix_sparse(k, G_vectors, ra, epsilon_a, epsilon_b, f)
        else:
            H = build_matrix(k, G_vectors, ra, epsilon_a, epsilon_b, f)

        # 使用选择的对角化方法
        eigenvalues = solve_eigenvalue_problem(H, num_bands, diag_method, eigsh_tol, eigsh_maxiter, idx)

        return (idx, np.sqrt(eigenvalues))
    except Exception as e:
        print(f"Error calculating point {idx}: {e}")
        # 返回零值作为fallback
        return (idx, np.zeros(num_bands))

# 生成FCC结构的倒格矢量
def generate_fcc_reciprocal_lattice(n_max, use_cutoff=True):
    """生成FCC结构的倒格矢量，可选择使用截断来减少矢量数量"""
    G_vectors = []

    # FCC结构的基本倒格矢量 (对于晶格常数a=1的FCC结构)
    # FCC的倒格子是BCC结构
    b1 = 2*np.pi*np.array([1, 1, -1])
    b2 = 2*np.pi*np.array([1, -1, 1])
    b3 = 2*np.pi*np.array([-1, 1, 1])

    # 计算截断半径 - 使用更合理的截断策略
    if use_cutoff:
        # 基于最大倒格矢量长度的截断
        max_G_length = 2*np.pi*n_max*np.sqrt(3)
        cutoff_radius = max_G_length
    else:
        cutoff_radius = float('inf')

    # 生成倒格矢量
    for n1 in range(-n_max, n_max+1):
        for n2 in range(-n_max, n_max+1):
            for n3 in range(-n_max, n_max+1):
                G = n1*b1 + n2*b2 + n3*b3
                G_mag = np.linalg.norm(G)

                # 应用截断条件
                if G_mag <= cutoff_radius:
                    G_vectors.append(G)

    # 按照G矢量的长度排序，有助于数值稳定性
    G_vectors = np.array(G_vectors)
    if len(G_vectors) > 0:
        G_magnitudes = np.linalg.norm(G_vectors, axis=1)
        sorted_indices = np.argsort(G_magnitudes)
        G_vectors = G_vectors[sorted_indices]

    return G_vectors

# 对能带进行排序和平滑处理的函数
def sort_bands(bands):
    """对能带进行排序以确保连续性"""
    sorted_bands = np.zeros_like(bands)
    sorted_bands[0] = bands[0]  # 第一个k点保持不变

    # 对每个后续k点
    for i in range(1, bands.shape[0]):
        prev_bands = sorted_bands[i-1]
        current_bands = bands[i]

        # 计算当前点的每个能带与前一点所有能带的距离
        distances = np.zeros((len(current_bands), len(prev_bands)))
        for j in range(len(current_bands)):
            for k in range(len(prev_bands)):
                distances[j, k] = abs(current_bands[j] - prev_bands[k])

        # 使用匈牙利算法找到最佳匹配
        row_ind, col_ind = linear_sum_assignment(distances)

        # 根据匹配重新排序
        sorted_current = np.zeros_like(current_bands)
        for j, k in zip(row_ind, col_ind):
            sorted_current[k] = current_bands[j]

        sorted_bands[i] = sorted_current

    return sorted_bands

def smooth_bands(bands, window_size=3):
    """使用移动平均对能带进行平滑处理"""
    smoothed_bands = np.zeros_like(bands)

    for i in range(bands.shape[1]):  # 对每个能带
        band = bands[:, i]

        # 简单移动平均
        smoothed_band = np.copy(band)
        for j in range(len(band)):
            start = max(0, j - window_size//2)
            end = min(len(band), j + window_size//2 + 1)
            smoothed_band[j] = np.mean(band[start:end])

        smoothed_bands[:, i] = smoothed_band

    return smoothed_bands

class PhotonicBandCalculator:
    def __init__(self, master):
        self.master = master
        self.master.title("FCC结构密堆积电介质球带结构计算器")
        self.master.geometry("1200x800")
        self.master.minsize(1000, 700)

        # 创建主框架
        self.main_frame = ttk.Frame(self.master)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建左侧参数面板 - 分为两列
        self.left_frame = ttk.Frame(self.main_frame)
        self.left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)

        # 第一列 - 基本参数
        self.params_frame = ttk.LabelFrame(self.left_frame, text="参数设置")
        self.params_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)

        # 第二列 - 计算选项
        self.options_frame = ttk.LabelFrame(self.left_frame, text="计算选项")
        self.options_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)

        # 创建右侧结果显示面板
        self.results_frame = ttk.LabelFrame(self.main_frame, text="带结构图")
        self.results_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建底部状态栏
        self.status_frame = ttk.Frame(self.master)
        self.status_frame.pack(side=tk.BOTTOM, fill=tk.X)

        # 增加一个进度条
        self.progress_var = tk.DoubleVar()
        self.progress = ttk.Progressbar(self.status_frame, variable=self.progress_var,
                                        mode='determinate', length=200)
        self.progress.pack(side=tk.RIGHT, padx=5, pady=2)

        # 状态文本显示
        self.status_var = tk.StringVar()
        self.status_var.set("准备就绪")
        self.status_label = ttk.Label(self.status_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=2)

        # 计算详情状态
        self.calc_status_var = tk.StringVar()
        self.calc_status_var.set("")
        self.calc_status_label = ttk.Label(self.status_frame, textvariable=self.calc_status_var, width=30)
        self.calc_status_label.pack(side=tk.RIGHT, padx=5, pady=2)

        # 初始化参数
        self.init_params()

        # 优化选项 - 移到这里，在create_params_widgets之前
        self.use_parallel = tk.BooleanVar(value=True)
        self.use_sparse = tk.BooleanVar(value=SPARSE_AVAILABLE)
        self.num_processes = tk.IntVar(value=min(4, mp.cpu_count()))
        self.use_cutoff = tk.BooleanVar(value=True)

        # 矩阵对角化选项
        self.diag_method = tk.StringVar(value="eigh")
        self.eigsh_tol = tk.DoubleVar(value=1e-8)  # 提高默认精度
        self.eigsh_maxiter = tk.IntVar(value=1000)

        # 带结构后处理选项
        self.sort_bands_var = tk.BooleanVar(value=True)
        self.smooth_bands_var = tk.BooleanVar(value=False)
        self.smooth_window = tk.IntVar(value=3)

        # 创建参数输入界面
        self.create_params_widgets()

        # 创建路径控制界面
        self.create_path_widgets()

        # 创建矩阵对角化选项
        self.create_diag_options()

        # 创建带结构后处理选项
        self.create_post_processing_options()

        # 创建按钮
        self.create_buttons()

        # 创建绘图区域
        self.create_plot_area()

        # 初始化计算线程
        self.calc_thread = None
        self.is_calculating = False
        self.cancel_calculation = False

        # 初始化带结构数据
        self.bands = None
        self.k_path = None
        self.k_labels = []
        self.k_label_positions = []

        # 高对称点缓存 - 用于确保相同标签点的坐标完全一致
        self.high_sym_points_cache = {}

    def init_params(self):
        # FCC密堆积结构的理论计算
        # FCC结构中，球心距离为a，球半径为a/(2√2)
        # 密堆积体积分数为π/(3√2) ≈ 0.74048
        # 对于单位晶格常数a=1，球半径为1/(2√2) ≈ 0.35355
        fcc_close_packed_radius = 1.0 / (2.0 * np.sqrt(2.0))  # ≈ 0.35355

        # 默认参数 - 使用FCC密堆积理论值
        self.ra = tk.DoubleVar(value=fcc_close_packed_radius)  # FCC密堆积球半径
        self.epsilon_a = tk.DoubleVar(value=11.56)  # 硅的介电常数
        self.epsilon_b = tk.DoubleVar(value=1.0)  # 空气
        self.n_max = tk.IntVar(value=4)  # 增加平面波数量以提高精度
        self.num_bands = tk.IntVar(value=10)  # 增加能带数量
        self.points_per_segment = tk.IntVar(value=20)  # 增加k点密度

        # FCC结构的高对称点 (标准FCC布里渊区)
        # 使用标准的FCC布里渊区高对称点定义
        a = 2*np.pi  # 晶格常数相关的归一化因子
        self.high_sym_points = {
            "Γ": np.array([0.0, 0.0, 0.0]),                    # 布里渊区中心
            "X": a*np.array([1.0, 0.0, 0.0]),                  # 立方面中心
            "L": a*np.array([0.5, 0.5, 0.5]),                  # 八面体顶点
            "W": a*np.array([1.0, 0.5, 0.0]),                  # 六边形面中心
            "K": a*np.array([0.75, 0.75, 0.0]),                # 六边形面顶点
            "U": a*np.array([1.0, 0.25, 0.25]),                # 四面体顶点
            "η": a*np.array([1.0, 1.0, 0.0])                   # eta点 (立方边中心)
        }

        # 用户指定的对称性点路径: X-U-L-η-X-W-K
        self.default_path = ["X", "U", "L", "η", "X", "W", "K"]
        self.path_points = self.default_path.copy()

        # 添加高对称点的坐标信息用于显示
        self.high_sym_coords_text = {
            "Γ": "(0, 0, 0)",
            "X": f"({a:.2f}, 0, 0)",
            "L": f"({a*0.5:.2f}, {a*0.5:.2f}, {a*0.5:.2f})",
            "W": f"({a:.2f}, {a*0.5:.2f}, 0)",
            "K": f"({a*0.75:.2f}, {a*0.75:.2f}, 0)",
            "U": f"({a:.2f}, {a*0.25:.2f}, {a*0.25:.2f})",
            "η": f"({a:.2f}, {a:.2f}, 0)"
        }

    def create_params_widgets(self):
        # 材料参数
        ttk.Label(self.params_frame, text="FCC密堆积材料参数", font=('Arial', 10, 'bold')).grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(10, 5))

        # 显示FCC密堆积理论值
        fcc_radius = 1.0 / (2.0 * np.sqrt(2.0))
        ttk.Label(self.params_frame, text=f"FCC密堆积半径: {fcc_radius:.5f}", font=('Arial', 8)).grid(row=1, column=0, columnspan=2, sticky=tk.W, padx=5, pady=2)

        ttk.Label(self.params_frame, text="电介质球半径 (ra):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.ra, width=10).grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(self.params_frame, text="球介电常数 (εa):").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.epsilon_a, width=10).grid(row=3, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(self.params_frame, text="背景介电常数 (εb):").grid(row=4, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.epsilon_b, width=10).grid(row=4, column=1, sticky=tk.W, padx=5, pady=2)

        # 计算参数
        ttk.Label(self.params_frame, text="计算参数", font=('Arial', 10, 'bold')).grid(row=5, column=0, columnspan=2, sticky=tk.W, pady=(15, 5))

        ttk.Label(self.params_frame, text="倒格矢量范围 (n_max):").grid(row=6, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Spinbox(self.params_frame, textvariable=self.n_max, from_=1, to=10, width=8).grid(row=6, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(self.params_frame, text="能带数量:").grid(row=7, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Spinbox(self.params_frame, textvariable=self.num_bands, from_=1, to=20, width=8).grid(row=7, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(self.params_frame, text="每段k点数量:").grid(row=8, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Spinbox(self.params_frame, textvariable=self.points_per_segment, from_=5, to=50, width=8).grid(row=8, column=1, sticky=tk.W, padx=5, pady=2)

    def create_path_widgets(self):
        # 高对称点路径设置
        ttk.Label(self.params_frame, text="高对称点路径", font=('Arial', 10, 'bold')).grid(row=9, column=0, columnspan=2, sticky=tk.W, pady=(15, 5))

        # 创建路径列表框
        self.path_frame = ttk.Frame(self.params_frame)
        self.path_frame.grid(row=10, column=0, columnspan=2, sticky=tk.W+tk.E, padx=5, pady=5)

        self.path_listbox = tk.Listbox(self.path_frame, height=8, width=25)
        self.path_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        scrollbar = ttk.Scrollbar(self.path_frame, orient="vertical", command=self.path_listbox.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.path_listbox.configure(yscrollcommand=scrollbar.set)

        # 添加默认路径到列表框
        for point in self.path_points:
            self.path_listbox.insert(tk.END, point)

        # 创建路径控制按钮
        self.path_buttons_frame = ttk.Frame(self.params_frame)
        self.path_buttons_frame.grid(row=11, column=0, columnspan=2, sticky=tk.W+tk.E, padx=5, pady=5)

        # 高对称点选择下拉框
        self.point_var = tk.StringVar()
        self.point_combo = ttk.Combobox(self.path_buttons_frame, textvariable=self.point_var,
                                         values=list(self.high_sym_points.keys()), width=5)
        self.point_combo.current(0)
        self.point_combo.pack(side=tk.LEFT, padx=2)

        # 添加点按钮
        ttk.Button(self.path_buttons_frame, text="添加", width=5,
                   command=self.add_point).pack(side=tk.LEFT, padx=2)

        # 删除点按钮
        ttk.Button(self.path_buttons_frame, text="删除", width=5,
                   command=self.remove_point).pack(side=tk.LEFT, padx=2)

        # 上移按钮
        ttk.Button(self.path_buttons_frame, text="上移", width=5,
                   command=self.move_point_up).pack(side=tk.LEFT, padx=2)

        # 下移按钮
        ttk.Button(self.path_buttons_frame, text="下移", width=5,
                   command=self.move_point_down).pack(side=tk.LEFT, padx=2)

        # 重置路径按钮
        ttk.Button(self.params_frame, text="重置为默认路径",
                   command=self.reset_path).grid(row=12, column=0, columnspan=2, sticky=tk.W+tk.E, padx=5, pady=5)

        # 高对称点信息显示
        ttk.Label(self.params_frame, text="FCC密堆积信息", font=('Arial', 10, 'bold')).grid(row=13, column=0, columnspan=2, sticky=tk.W, pady=(15, 5))

        # 创建信息显示文本框
        self.info_frame = ttk.Frame(self.params_frame)
        self.info_frame.grid(row=14, column=0, columnspan=2, sticky=tk.W+tk.E, padx=5, pady=5)

        self.info_text = tk.Text(self.info_frame, height=6, width=30, font=('Courier', 8))
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        info_scrollbar = ttk.Scrollbar(self.info_frame, orient="vertical", command=self.info_text.yview)
        info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.info_text.configure(yscrollcommand=info_scrollbar.set)

        # 填充高对称点信息
        self.update_high_sym_info()

    def create_diag_options(self):
        # 矩阵对角化选项
        ttk.Label(self.options_frame, text="矩阵对角化选项", font=('Arial', 10, 'bold')).grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(10, 5))

        ttk.Label(self.options_frame, text="对角化方法:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)

        # 可用的对角化方法
        available_methods = ["eigh", "eigsh"]
        if SPARSE_AVAILABLE:
            available_methods.extend(["lobpcg", "arpack"])
        if DAVIDSON_AVAILABLE:
            available_methods.append("davidson")

        diag_methods = ttk.Combobox(self.options_frame, textvariable=self.diag_method,
                                   values=available_methods, width=10)
        diag_methods.grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(self.options_frame, text="收敛容差:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(self.options_frame, textvariable=self.eigsh_tol, width=10).grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(self.options_frame, text="最大迭代次数:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(self.options_frame, textvariable=self.eigsh_maxiter, width=10).grid(row=3, column=1, sticky=tk.W, padx=5, pady=2)

        # 添加优化选项
        ttk.Label(self.options_frame, text="计算优化选项", font=('Arial', 10, 'bold')).grid(row=4, column=0, columnspan=2, sticky=tk.W, pady=(15, 5))

        ttk.Checkbutton(self.options_frame, text="使用并行计算", variable=self.use_parallel).grid(row=5, column=0, columnspan=2, sticky=tk.W, padx=5, pady=2)

        if SPARSE_AVAILABLE:
            ttk.Checkbutton(self.options_frame, text="使用稀疏矩阵", variable=self.use_sparse).grid(row=6, column=0, columnspan=2, sticky=tk.W, padx=5, pady=2)

        ttk.Label(self.options_frame, text="并行进程数:").grid(row=7, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Spinbox(self.options_frame, textvariable=self.num_processes, from_=1, to=mp.cpu_count(), width=8).grid(row=7, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Checkbutton(self.options_frame, text="使用倒格矢量截断", variable=self.use_cutoff).grid(row=8, column=0, columnspan=2, sticky=tk.W, padx=5, pady=2)

    def create_post_processing_options(self):
        # 带结构后处理选项
        ttk.Label(self.options_frame, text="带结构后处理", font=('Arial', 10, 'bold')).grid(row=9, column=0, columnspan=2, sticky=tk.W, pady=(15, 5))

        ttk.Checkbutton(self.options_frame, text="能带排序", variable=self.sort_bands_var).grid(
            row=10, column=0, columnspan=2, sticky=tk.W, padx=5, pady=2)

        ttk.Checkbutton(self.options_frame, text="能带平滑", variable=self.smooth_bands_var).grid(
            row=11, column=0, columnspan=2, sticky=tk.W, padx=5, pady=2)

        ttk.Label(self.options_frame, text="平滑窗口大小:").grid(row=12, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Spinbox(self.options_frame, textvariable=self.smooth_window, from_=3, to=11, increment=2, width=5).grid(
            row=12, column=1, sticky=tk.W, padx=5, pady=2)

    def create_buttons(self):
        # 创建操作按钮
        buttons_frame = ttk.Frame(self.options_frame)
        buttons_frame.grid(row=13, column=0, columnspan=2, sticky=tk.W+tk.E, pady=(20, 5))

        self.calc_button = ttk.Button(buttons_frame, text="计算带结构",
                                    command=self.start_calculation)
        self.calc_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=5)

        self.cancel_button = ttk.Button(buttons_frame, text="取消计算",
                                      command=self.cancel_calculation_command, state=tk.DISABLED)
        self.cancel_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=5)

        ttk.Button(buttons_frame, text="保存结果",
                   command=self.save_results).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=5)

    def create_plot_area(self):
        # 创建matplotlib图形
        self.fig = Figure(figsize=(6, 5), dpi=100)
        self.ax = self.fig.add_subplot(111)
        self.ax.set_xlabel('Wave Vector k')
        self.ax.set_ylabel('Frequency ω/c')
        self.ax.set_title('Photonic Band Structure of FCC Dielectric Spheres')
        self.ax.grid(True, linestyle='--', alpha=0.7)

        # 在Tkinter窗口中嵌入matplotlib图形
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.results_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # 添加matplotlib工具栏
        toolbar_frame = ttk.Frame(self.results_frame)
        toolbar_frame.pack(fill=tk.X)
        toolbar = NavigationToolbar2Tk(self.canvas, toolbar_frame)
        toolbar.update()

    def add_point(self):
        point = self.point_var.get()
        if point:
            selected = self.path_listbox.curselection()
            if selected:
                index = selected[0]
                self.path_listbox.insert(index+1, point)
                self.path_points.insert(index+1, point)
            else:
                self.path_listbox.insert(tk.END, point)
                self.path_points.append(point)

    def remove_point(self):
        selected = self.path_listbox.curselection()
        if selected:
            index = selected[0]
            self.path_listbox.delete(index)
            self.path_points.pop(index)

    def move_point_up(self):
        selected = self.path_listbox.curselection()
        if selected and selected[0] > 0:
            index = selected[0]
            point = self.path_points[index]
            self.path_listbox.delete(index)
            self.path_points.pop(index)
            self.path_listbox.insert(index-1, point)
            self.path_points.insert(index-1, point)
            self.path_listbox.selection_set(index-1)

    def move_point_down(self):
        selected = self.path_listbox.curselection()
        if selected and selected[0] < len(self.path_points) - 1:
            index = selected[0]
            point = self.path_points[index]
            self.path_listbox.delete(index)
            self.path_points.pop(index)
            self.path_listbox.insert(index+1, point)
            self.path_points.insert(index+1, point)
            self.path_listbox.selection_set(index+1)

    def reset_path(self):
        self.path_listbox.delete(0, tk.END)
        self.path_points = self.default_path.copy()
        for point in self.path_points:
            self.path_listbox.insert(tk.END, point)

    def update_high_sym_info(self):
        """更新高对称点信息显示"""
        self.info_text.delete(1.0, tk.END)

        # 计算FCC密堆积信息
        fcc_radius = 1.0 / (2.0 * np.sqrt(2.0))
        fcc_volume_fraction = np.pi / (3.0 * np.sqrt(2.0))

        info_content = "FCC密堆积光子晶体:\n"
        info_content += "=" * 25 + "\n"
        info_content += f"理论球半径: {fcc_radius:.5f}\n"
        info_content += f"密堆积体积分数: {fcc_volume_fraction:.5f}\n"
        info_content += f"当前球半径: {self.ra.get():.5f}\n\n"

        info_content += "高对称点坐标:\n"
        info_content += "-" * 20 + "\n"

        for label, coords in self.high_sym_points.items():
            coord_str = f"({coords[0]:.3f}, {coords[1]:.3f}, {coords[2]:.3f})"
            info_content += f"{label:2s}: {coord_str}\n"

        info_content += "\n当前路径: " + " → ".join(self.path_points) + "\n"
        info_content += "\n点说明:\n"
        info_content += "Γ: 布里渊区中心\n"
        info_content += "X: 立方面中心\n"
        info_content += "L: 八面体顶点\n"
        info_content += "W: 六边形面中心\n"
        info_content += "K: 六边形面顶点\n"
        info_content += "U: 四面体顶点\n"
        info_content += "η: 立方边中心\n"

        self.info_text.insert(1.0, info_content)
        self.info_text.configure(state=tk.DISABLED)

    def start_calculation(self):
        if self.is_calculating:
            messagebox.showinfo("计算中", "计算正在进行，请等待...")
            return

        # 检查路径是否至少有两个点
        if len(self.path_points) < 2:
            messagebox.showerror("路径错误", "高对称点路径至少需要两个点!")
            return

        # 清除之前的图形
        self.ax.clear()
        self.canvas.draw()

        # 更新状态
        self.is_calculating = True
        self.cancel_calculation = False
        self.status_var.set("计算中...")
        self.calc_status_var.set("准备计算...")
        self.progress_var.set(0)

        # 更新按钮状态
        self.calc_button.configure(state=tk.DISABLED)
        self.cancel_button.configure(state=tk.NORMAL)

        # 在新线程中启动计算
        self.calc_thread = threading.Thread(target=self.calculate_band_structure)
        self.calc_thread.daemon = True
        self.calc_thread.start()

    def cancel_calculation_command(self):
        if self.is_calculating:
            self.cancel_calculation = True
            self.status_var.set("正在取消计算...")
            self.calc_status_var.set("等待线程结束...")

    def calculate_band_structure(self):
        try:
            # 获取参数
            ra = self.ra.get()
            epsilon_a = self.epsilon_a.get()
            epsilon_b = self.epsilon_b.get()
            n_max = self.n_max.get()
            num_bands = self.num_bands.get()
            points_per_segment = self.points_per_segment.get()
            use_cutoff = self.use_cutoff.get()

            # 计算体积分数
            f = 4*np.pi*(ra**3)/3

            # 生成倒格矢量
            G_vectors = generate_fcc_reciprocal_lattice(n_max, use_cutoff)
            self.update_status(f"使用 {len(G_vectors)} 个平面波")
            self.update_calc_status("生成倒格矢量完成")

            if self.cancel_calculation:
                self.cleanup_after_calculation("计算已取消")
                return

            # 构建k路径 - 使用改进的路径构建方法
            self.k_path, self.k_labels, self.k_label_positions = self.build_k_path_improved(points_per_segment)
            total_points = len(self.k_path)
            self.update_calc_status(f"将计算 {total_points} 个k点")

            if self.cancel_calculation:
                self.cleanup_after_calculation("计算已取消")
                return

            # 计算带结构 - 根据用户选择决定是否使用并行计算
            self.update_status("开始计算带结构...")

            if self.use_parallel.get():
                self.bands = self.calculate_bands_parallel(self.k_path, G_vectors, ra, epsilon_a, epsilon_b, f, num_bands)
            else:
                self.bands = self.calculate_bands(self.k_path, G_vectors, ra, epsilon_a, epsilon_b, f, num_bands)

            if self.cancel_calculation:
                self.cleanup_after_calculation("计算已取消")
                return

            # 修正相同高对称点的频率值
            self.bands = self.ensure_high_symmetry_consistency(self.bands)

            # 在主线程中更新图形
            self.master.after(0, self.update_plot)

        except Exception as e:
            self.update_status(f"计算出错: {str(e)}")
            self.update_calc_status("计算失败")
            self.cleanup_after_calculation()
            messagebox.showerror("计算错误", str(e))

    def cleanup_after_calculation(self, status_message="计算完成"):
        """清理计算后的状态"""
        self.is_calculating = False
        self.cancel_calculation = False
        self.update_status(status_message)

        # 在主线程中更新按钮状态
        self.master.after(0, lambda: self.calc_button.configure(state=tk.NORMAL))
        self.master.after(0, lambda: self.cancel_button.configure(state=tk.DISABLED))

    def update_status(self, message):
        self.master.after(0, lambda: self.status_var.set(message))

    def update_calc_status(self, message):
        self.master.after(0, lambda: self.calc_status_var.set(message))

    def update_progress(self, value):
        self.master.after(0, lambda: self.progress_var.set(value))

    def build_k_path_improved(self, points_per_segment):
        """改进的k路径构建函数，确保相同高对称点的完全一致性"""
        k_path = []
        k_labels = []
        k_label_positions = []

        # 初始化高对称点缓存
        self.high_sym_points_cache = {}
        for label, point in self.high_sym_points.items():
            # 使用精确的数组副本，确保不同引用使用完全相同的值
            self.high_sym_points_cache[label] = np.array(point, dtype=np.float64)

        current_position = 0

        for i in range(len(self.path_points) - 1):
            start_label = self.path_points[i]
            end_label = self.path_points[i+1]

            # 使用缓存的精确坐标
            start_k = self.high_sym_points_cache[start_label]
            end_k = self.high_sym_points_cache[end_label]

            # 添加路径段
            for j in range(points_per_segment):
                if j == 0 and i == 0:
                    # 第一个路径段的起点
                    k = np.copy(start_k)
                    k_path.append(k)
                    k_labels.append(start_label)
                    k_label_positions.append(current_position)
                    current_position += 1
                elif j == 0:
                    # 其他路径段的起点 - 已经在上一段的终点添加过
                    continue
                elif j == points_per_segment - 1:
                    # 终点
                    k = np.copy(end_k)
                    k_path.append(k)
                    k_labels.append(end_label)
                    k_label_positions.append(current_position)
                    current_position += 1
                else:
                    # 中间点
                    t = j / (points_per_segment - 1)
                    k = (1-t)*start_k + t*end_k
                    k_path.append(k)
                    current_position += 1

        return np.array(k_path), k_labels, k_label_positions

    def ensure_high_symmetry_consistency(self, bands):
        """确保相同高对称点的频率值完全一致"""
        # 创建标签到索引的映射
        label_to_indices = {}
        for i, label in enumerate(self.k_labels):
            if label not in label_to_indices:
                label_to_indices[label] = []
            label_to_indices[label].append(self.k_label_positions[i])

        # 对于每个出现多次的标签
        for label, positions in label_to_indices.items():
            if len(positions) > 1:
                # 计算这些位置处频率值的平均值
                avg_values = np.zeros(bands.shape[1])
                for pos in positions:
                    avg_values += bands[pos]
                avg_values /= len(positions)

                # 将所有位置的值设为平均值
                for pos in positions:
                    bands[pos] = avg_values.copy()

        return bands

    def calculate_bands(self, k_path, G_vectors, ra, epsilon_a, epsilon_b, f, num_bands=5):
        """串行计算指定k路径上的带结构"""
        bands = []

        start_time = time.time()
        total_points = len(k_path)

        # 决定是否使用稀疏矩阵
        use_sparse = self.use_sparse.get() and SPARSE_AVAILABLE and len(G_vectors) > 100
        diag_method = self.diag_method.get()
        eigsh_tol = self.eigsh_tol.get()
        eigsh_maxiter = self.eigsh_maxiter.get()

        # 创建高对称点到索引的映射
        high_sym_indices = {}
        for i, label in enumerate(self.k_labels):
            pos = self.k_label_positions[i]
            if label not in high_sym_indices:
                high_sym_indices[label] = []
            high_sym_indices[label].append(pos)

        # 预先计算高对称点的频率
        high_sym_values = {}
        for label, positions in high_sym_indices.items():
            if len(positions) > 0:
                pos = positions[0]  # 取第一个位置
                k = k_path[pos]

                # 构建矩阵并求解特征值问题
                if use_sparse:
                    H = build_matrix_sparse(k, G_vectors, ra, epsilon_a, epsilon_b, f)
                else:
                    H = build_matrix(k, G_vectors, ra, epsilon_a, epsilon_b, f)

                eigenvalues = solve_eigenvalue_problem(H, num_bands, diag_method, eigsh_tol, eigsh_maxiter)
                high_sym_values[label] = np.sqrt(eigenvalues)

        for i, k in enumerate(k_path):
            if self.cancel_calculation:
                return np.zeros((len(k_path), num_bands))

            try:
                # 检查当前点是否是高对称点
                is_high_sym = False
                for j, pos in enumerate(self.k_label_positions):
                    if i == pos:
                        label = self.k_labels[j]
                        bands.append(high_sym_values[label])
                        is_high_sym = True
                        break

                if not is_high_sym:
                    # 构建矩阵
                    if use_sparse:
                        H = build_matrix_sparse(k, G_vectors, ra, epsilon_a, epsilon_b, f)
                    else:
                        H = build_matrix(k, G_vectors, ra, epsilon_a, epsilon_b, f)

                    # 求解特征值问题
                    eigenvalues = solve_eigenvalue_problem(H, num_bands, diag_method, eigsh_tol, eigsh_maxiter, i)
                    bands.append(np.sqrt(eigenvalues))
            except Exception as e:
                self.update_status(f"计算点 {i} 出错: {e}")
                # 使用零值作为fallback
                bands.append(np.zeros(num_bands))

            # 显示进度
            progress_percent = (i+1) / total_points * 100
            self.update_progress(progress_percent)

            if (i+1) % 5 == 0 or i+1 == total_points:
                elapsed = time.time() - start_time
                remaining = (elapsed / (i+1)) * (total_points - (i+1)) if i < total_points - 1 else 0
                self.update_status(f"计算中: 已用时 {elapsed:.1f}秒, 预计剩余 {remaining:.1f}秒")
                self.update_calc_status(f"已完成 {i+1}/{total_points} 点 ({progress_percent:.1f}%)")

        return np.array(bands)

    def calculate_bands_parallel(self, k_path, G_vectors, ra, epsilon_a, epsilon_b, f, num_bands=5):
        """并行计算指定k路径上的带结构"""
        total_points = len(k_path)
        start_time = time.time()

        # 配置并行计算
        use_sparse = self.use_sparse.get() and SPARSE_AVAILABLE and len(G_vectors) > 100
        num_processes = min(self.num_processes.get(), mp.cpu_count())
        diag_method = self.diag_method.get()
        eigsh_tol = self.eigsh_tol.get()
        eigsh_maxiter = self.eigsh_maxiter.get()

        self.update_calc_status(f"使用 {num_processes} 个进程并行计算")

        # 创建进程池
        pool = mp.Pool(processes=num_processes)

        # 设置任务列表
        tasks = []
        for i, k in enumerate(k_path):
            tasks.append((i, k, G_vectors, ra, epsilon_a, epsilon_b, f, num_bands, use_sparse,
                         diag_method, eigsh_tol, eigsh_maxiter))

        # 启动异步计算 - 使用全局函数
        results_async = [pool.apply_async(calculate_single_point, (task,)) for task in tasks]

        # 等待结果并更新进度
        results = [None] * total_points
        for i, res_async in enumerate(results_async):
            if self.cancel_calculation:
                pool.terminate()
                pool.join()
                return np.zeros((len(k_path), num_bands))

            try:
                idx, band_values = res_async.get(timeout=300)  # 设置超时时间
                results[idx] = band_values
            except Exception as e:
                self.update_status(f"计算点 {i} 出错: {e}")
                # 使用零值作为fallback
                results[i] = np.zeros(num_bands)

            # 更新进度
            progress_percent = (i+1) / total_points * 100
            self.update_progress(progress_percent)

            if (i+1) % 5 == 0 or i+1 == total_points:
                elapsed = time.time() - start_time
                remaining = (elapsed / (i+1)) * (total_points - (i+1)) if i < total_points - 1 else 0
                self.update_status(f"计算中: 已用时 {elapsed:.1f}秒, 预计剩余 {remaining:.1f}秒")
                self.update_calc_status(f"已完成 {i+1}/{total_points} 点 ({progress_percent:.1f}%)")

        pool.close()
        pool.join()

        return np.array(results)

    def update_plot(self):
        try:
            # 清除之前的图形
            self.ax.clear()

            # 应用后处理
            processed_bands = np.copy(self.bands)

            # 能带排序
            if self.sort_bands_var.get():
                processed_bands = sort_bands(processed_bands)

            # 能带平滑
            if self.smooth_bands_var.get():
                processed_bands = smooth_bands(processed_bands, self.smooth_window.get())

            # 绘制能带 - 使用不同颜色区分不同能带
            colors = ['blue', 'red', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']
            for i in range(processed_bands.shape[1]):
                color = colors[i % len(colors)]
                self.ax.plot(range(len(self.k_path)), processed_bands[:, i],
                           color=color, linewidth=1.5, label=f'Band {i+1}')

            # 设置x轴标签 - 包含坐标信息
            enhanced_labels = []
            for label in self.k_labels:
                if label in self.high_sym_coords_text:
                    enhanced_labels.append(f'{label}\n{self.high_sym_coords_text[label]}')
                else:
                    enhanced_labels.append(label)

            self.ax.set_xticks(self.k_label_positions)
            self.ax.set_xticklabels(enhanced_labels, fontsize=9)

            # 在高对称点处绘制垂直线
            for i, pos in enumerate(self.k_label_positions):
                self.ax.axvline(x=pos, color='k', linestyle='-', alpha=0.3, linewidth=0.8)

            # 添加带隙分析
            self.analyze_and_mark_bandgaps(processed_bands)

            # 设置标题和标签
            self.ax.set_xlabel('Wave Vector k', fontsize=12)
            self.ax.set_ylabel('Frequency ω/c', fontsize=12)

            # 计算体积分数
            f = 4*np.pi*(self.ra.get()**3)/3
            title = (f'FCC光子晶体带结构\n'
                    f'εₐ={self.epsilon_a.get():.1f}, εᵦ={self.epsilon_b.get():.1f}, '
                    f'rₐ={self.ra.get():.2f}, f={f:.3f}')
            self.ax.set_title(title, fontsize=11)

            self.ax.grid(True, linestyle='--', alpha=0.7)

            # 添加图例
            if processed_bands.shape[1] <= 8:  # 只在能带数量不太多时显示图例
                self.ax.legend(loc='upper right', fontsize=8)

            # 更新图形
            self.canvas.draw()

            # 更新状态
            self.cleanup_after_calculation("计算完成")
            self.calc_status_var.set(f"完成计算，共 {len(self.k_path)} 个点")
            self.progress_var.set(100)

        except Exception as e:
            self.status_var.set(f"绘图出错: {str(e)}")
            self.calc_status_var.set("绘图失败")
            self.cleanup_after_calculation("绘图出错")
            messagebox.showerror("绘图错误", str(e))

    def analyze_and_mark_bandgaps(self, bands):
        """分析并标记带隙 - 改进的全带隙检测"""
        try:
            # 跳过零频率模式（通常是第一个模式）
            start_band = 1 if bands[0, 0] < 1e-6 else 0

            # 寻找全带隙（在整个k路径上都存在的带隙）
            complete_bandgaps = []

            for i in range(start_band, bands.shape[1] - 1):
                # 计算相邻能带之间在所有k点的间隙
                upper_band = bands[:, i+1]
                lower_band = bands[:, i]

                # 找到下能带的全局最大值和上能带的全局最小值
                lower_max = np.max(lower_band)
                upper_min = np.min(upper_band)

                # 检查是否存在完整带隙
                if upper_min > lower_max:
                    gap_size = upper_min - lower_max
                    gap_center = (upper_min + lower_max) / 2

                    # 计算相对带隙大小
                    relative_gap = gap_size / gap_center if gap_center > 0 else 0

                    complete_bandgaps.append({
                        'lower_band': i,
                        'upper_band': i+1,
                        'gap_size': gap_size,
                        'gap_center': gap_center,
                        'lower_max': lower_max,
                        'upper_min': upper_min,
                        'relative_gap': relative_gap
                    })

            # 标记显著的完整带隙
            significant_gaps = []
            gap_colors = ['yellow', 'lightgreen', 'lightblue', 'lightcoral', 'lightsalmon']

            for idx, gap in enumerate(complete_bandgaps):
                # 降低阈值以显示更多带隙
                if gap['relative_gap'] > 0.01:  # 1%以上的相对带隙
                    significant_gaps.append(gap)

                    # 在图上标记带隙
                    color = gap_colors[idx % len(gap_colors)]
                    self.ax.axhspan(gap['lower_max'], gap['upper_min'],
                                   alpha=0.3, color=color,
                                   label=f'完整带隙 {gap["lower_band"]+1}-{gap["upper_band"]+1}')

                    # 添加带隙大小标注
                    text_x = len(self.k_path) * (0.05 + idx * 0.15)
                    self.ax.text(text_x, gap['gap_center'],
                               f'Δω/ω₀ = {gap["relative_gap"]:.1%}\nΔω = {gap["gap_size"]:.3f}',
                               fontsize=7, ha='center',
                               bbox=dict(boxstyle="round,pad=0.3",
                                       facecolor=color, alpha=0.8))

            # 分析局部带隙（在某些k点区域存在的带隙）
            self.analyze_local_bandgaps(bands, start_band)

            # 更新状态信息
            if significant_gaps:
                gap_info = f"发现 {len(significant_gaps)} 个完整带隙"
                for i, gap in enumerate(significant_gaps):
                    gap_info += f"\n完整带隙{i+1}: Δω={gap['gap_size']:.4f}, 相对大小={gap['relative_gap']:.1%}"
                self.update_calc_status(gap_info)
            else:
                self.update_calc_status("未发现显著的完整带隙")

        except Exception as e:
            print(f"带隙分析出错: {e}")
            self.update_calc_status("带隙分析失败")

    def analyze_local_bandgaps(self, bands, start_band):
        """分析局部带隙"""
        try:
            # 在高对称点检查局部带隙
            for i, pos in enumerate(self.k_label_positions):
                if pos < len(bands):
                    label = self.k_labels[i]
                    point_bands = bands[pos, start_band:]

                    # 检查相邻能带间隙
                    for j in range(len(point_bands) - 1):
                        gap = point_bands[j+1] - point_bands[j]
                        if gap > 0.1:  # 显著的局部间隙
                            # 在该点标记局部间隙
                            self.ax.plot(pos, (point_bands[j] + point_bands[j+1])/2,
                                       'ro', markersize=4, alpha=0.7)

        except Exception as e:
            print(f"局部带隙分析出错: {e}")

    def save_results(self):
        if self.bands is None:
            messagebox.showinfo("保存结果", "请先计算带结构!")
            return

        # 保存图像
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".png",
                filetypes=[("PNG图像", "*.png"), ("PDF文档", "*.pdf"), ("所有文件", "*.*")],
                title="保存带结构图"
            )
            if filename:
                self.fig.savefig(filename, dpi=300, bbox_inches='tight')
                self.status_var.set(f"图像已保存至 {os.path.basename(filename)}")

                # 询问是否保存数据
                if messagebox.askyesno("保存数据", "是否也保存带结构数据?"):
                    data_filename = os.path.splitext(filename)[0] + ".npz"
                    np.savez(data_filename,
                             bands=self.bands,
                             k_path=self.k_path,
                             k_labels=np.array(self.k_labels, dtype=object),
                             k_label_positions=np.array(self.k_label_positions))
                    self.status_var.set(f"图像和数据已保存")
        except Exception as e:
            messagebox.showerror("保存错误", str(e))

def main():
    root = tk.Tk()
    app = PhotonicBandCalculator(root)
    root.mainloop()

if __name__ == "__main__":
    # Windows下PyInstaller打包multiprocessing需要使用spawn
    if sys.platform.startswith('win'):
        mp.set_start_method('spawn', force=True)
        mp.freeze_support()
    main()