(['G:\\wangchaoyu\\pwe-calculate\\photonic_band_calculator.py'],
 ['G:\\wangchaoyu\\pwe-calculate'],
 ['numpy',
  'matplotlib',
  'scipy',
  'numba',
  'tkinter',
  'multiprocessing',
  'numpy.core._methods',
  'numpy.lib.format',
  'numpy.testing',
  'matplotlib.backends.backend_tkagg',
  'scipy.sparse.csgraph._validation',
  'scipy.special.cython_special',
  'scipy.linalg.cython_lapack',
  'scipy.linalg.cython_blas',
  'scipy.integrate._odepack',
  'scipy.integrate._quadpack',
  'tkinter.filedialog',
  'multiprocessing.pool',
  'multiprocessing.managers',
  'multiprocessing.popen_spawn_win32',
  'pkg_resources.py2_warn'],
 ['C:\\Python\\Python310\\Lib\\site-packages\\numpy\\_pyinstaller',
  'C:\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
  'C:\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib'],
 {},
 [],
 [],
 False,
 {},
 [],
 [('matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.pdf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back_large.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.pdf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave_large.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.pdf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward_large.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.pdf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.pdf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help_large.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.pdf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home_large.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.pdf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move_large.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.pdf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots_large.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\kpsewhich.lua',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\kpsewhich.lua',
   'DATA'),
  ('matplotlib\\mpl-data\\matplotlibrc',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\matplotlibrc',
   'DATA'),
  ('matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\README.txt',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\README.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Stocks.csv',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Stocks.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\goog.npz',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\goog.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\logo2.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\logo2.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\msft.csv',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\msft.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\petroff10.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\petroff10.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-bright.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-bright.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-colorblind.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-colorblind.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark-palette.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark-palette.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-darkgrid.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-darkgrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-deep.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-deep.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-muted.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-muted.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-notebook.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-notebook.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-paper.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-paper.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-pastel.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-pastel.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-poster.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-poster.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-talk.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-talk.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-ticks.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-ticks.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-white.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-white.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-whitegrid.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-whitegrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'DATA')],
 '3.10.8 (tags/v3.10.8:aaaf517, Oct 11 2022, 16:50:30) [MSC v.1933 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Python\\Python310\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Python\\Python310\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Python\\Python310\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Python\\Python310\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_mplconfig',
   'C:\\Python\\Python310\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_mplconfig.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Python\\Python310\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\Python\\Python310\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('photonic_band_calculator',
   'G:\\wangchaoyu\\pwe-calculate\\photonic_band_calculator.py',
   'PYSOURCE')],
 [('multiprocessing.popen_forkserver',
   'C:\\Python\\Python310\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Python\\Python310\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Python\\Python310\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Python\\Python310\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('signal', 'C:\\Python\\Python310\\lib\\signal.py', 'PYMODULE'),
  ('selectors', 'C:\\Python\\Python310\\lib\\selectors.py', 'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Python\\Python310\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc', 'C:\\Python\\Python310\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('gzip', 'C:\\Python\\Python310\\lib\\gzip.py', 'PYMODULE'),
  ('argparse', 'C:\\Python\\Python310\\lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'C:\\Python\\Python310\\lib\\textwrap.py', 'PYMODULE'),
  ('shutil', 'C:\\Python\\Python310\\lib\\shutil.py', 'PYMODULE'),
  ('zipfile', 'C:\\Python\\Python310\\lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'C:\\Python\\Python310\\lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\Python\\Python310\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Python\\Python310\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._common',
   'C:\\Python\\Python310\\lib\\importlib\\_common.py',
   'PYMODULE'),
  ('importlib._adapters',
   'C:\\Python\\Python310\\lib\\importlib\\_adapters.py',
   'PYMODULE'),
  ('typing', 'C:\\Python\\Python310\\lib\\typing.py', 'PYMODULE'),
  ('importlib.abc',
   'C:\\Python\\Python310\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Python\\Python310\\lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Python\\Python310\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Python\\Python310\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Python\\Python310\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy', 'C:\\Python\\Python310\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'C:\\Python\\Python310\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Python\\Python310\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('string', 'C:\\Python\\Python310\\lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'C:\\Python\\Python310\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Python\\Python310\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib', 'C:\\Python\\Python310\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.iterators',
   'C:\\Python\\Python310\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Python\\Python310\\lib\\email\\generator.py',
   'PYMODULE'),
  ('random', 'C:\\Python\\Python310\\lib\\random.py', 'PYMODULE'),
  ('statistics', 'C:\\Python\\Python310\\lib\\statistics.py', 'PYMODULE'),
  ('fractions', 'C:\\Python\\Python310\\lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'C:\\Python\\Python310\\lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'C:\\Python\\Python310\\lib\\hashlib.py', 'PYMODULE'),
  ('logging', 'C:\\Python\\Python310\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'C:\\Python\\Python310\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'C:\\Python\\Python310\\lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'C:\\Python\\Python310\\lib\\dataclasses.py', 'PYMODULE'),
  ('inspect', 'C:\\Python\\Python310\\lib\\inspect.py', 'PYMODULE'),
  ('token', 'C:\\Python\\Python310\\lib\\token.py', 'PYMODULE'),
  ('dis', 'C:\\Python\\Python310\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'C:\\Python\\Python310\\lib\\opcode.py', 'PYMODULE'),
  ('ast', 'C:\\Python\\Python310\\lib\\ast.py', 'PYMODULE'),
  ('_compat_pickle',
   'C:\\Python\\Python310\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('bisect', 'C:\\Python\\Python310\\lib\\bisect.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Python\\Python310\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Python\\Python310\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Python\\Python310\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Python\\Python310\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Python\\Python310\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header', 'C:\\Python\\Python310\\lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'C:\\Python\\Python310\\lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'C:\\Python\\Python310\\lib\\email\\utils.py', 'PYMODULE'),
  ('email._parseaddr',
   'C:\\Python\\Python310\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'C:\\Python\\Python310\\lib\\calendar.py', 'PYMODULE'),
  ('quopri', 'C:\\Python\\Python310\\lib\\quopri.py', 'PYMODULE'),
  ('getopt', 'C:\\Python\\Python310\\lib\\getopt.py', 'PYMODULE'),
  ('uu', 'C:\\Python\\Python310\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'C:\\Python\\Python310\\lib\\optparse.py', 'PYMODULE'),
  ('email', 'C:\\Python\\Python310\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'C:\\Python\\Python310\\lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser',
   'C:\\Python\\Python310\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('csv', 'C:\\Python\\Python310\\lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'C:\\Python\\Python310\\lib\\importlib\\readers.py',
   'PYMODULE'),
  ('tokenize', 'C:\\Python\\Python310\\lib\\tokenize.py', 'PYMODULE'),
  ('pathlib', 'C:\\Python\\Python310\\lib\\pathlib.py', 'PYMODULE'),
  ('contextlib', 'C:\\Python\\Python310\\lib\\contextlib.py', 'PYMODULE'),
  ('importlib.util',
   'C:\\Python\\Python310\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('tarfile', 'C:\\Python\\Python310\\lib\\tarfile.py', 'PYMODULE'),
  ('lzma', 'C:\\Python\\Python310\\lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'C:\\Python\\Python310\\lib\\bz2.py', 'PYMODULE'),
  ('fnmatch', 'C:\\Python\\Python310\\lib\\fnmatch.py', 'PYMODULE'),
  ('copy', 'C:\\Python\\Python310\\lib\\copy.py', 'PYMODULE'),
  ('gettext', 'C:\\Python\\Python310\\lib\\gettext.py', 'PYMODULE'),
  ('_compression', 'C:\\Python\\Python310\\lib\\_compression.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Python\\Python310\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Python\\Python310\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'C:\\Python\\Python310\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Python\\Python310\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Python\\Python310\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Python\\Python310\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass', 'C:\\Python\\Python310\\lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'C:\\Python\\Python310\\lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'C:\\Python\\Python310\\lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'C:\\Python\\Python310\\lib\\netrc.py', 'PYMODULE'),
  ('shlex', 'C:\\Python\\Python310\\lib\\shlex.py', 'PYMODULE'),
  ('mimetypes', 'C:\\Python\\Python310\\lib\\mimetypes.py', 'PYMODULE'),
  ('http.cookiejar',
   'C:\\Python\\Python310\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http', 'C:\\Python\\Python310\\lib\\http\\__init__.py', 'PYMODULE'),
  ('ssl', 'C:\\Python\\Python310\\lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'C:\\Python\\Python310\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error', 'C:\\Python\\Python310\\lib\\urllib\\error.py', 'PYMODULE'),
  ('xml.sax', 'C:\\Python\\Python310\\lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Python\\Python310\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Python\\Python310\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Python\\Python310\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('urllib.parse', 'C:\\Python\\Python310\\lib\\urllib\\parse.py', 'PYMODULE'),
  ('http.client', 'C:\\Python\\Python310\\lib\\http\\client.py', 'PYMODULE'),
  ('decimal', 'C:\\Python\\Python310\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Python\\Python310\\lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'C:\\Python\\Python310\\lib\\contextvars.py', 'PYMODULE'),
  ('datetime', 'C:\\Python\\Python310\\lib\\datetime.py', 'PYMODULE'),
  ('_strptime', 'C:\\Python\\Python310\\lib\\_strptime.py', 'PYMODULE'),
  ('base64', 'C:\\Python\\Python310\\lib\\base64.py', 'PYMODULE'),
  ('hmac', 'C:\\Python\\Python310\\lib\\hmac.py', 'PYMODULE'),
  ('tempfile', 'C:\\Python\\Python310\\lib\\tempfile.py', 'PYMODULE'),
  ('struct', 'C:\\Python\\Python310\\lib\\struct.py', 'PYMODULE'),
  ('socket', 'C:\\Python\\Python310\\lib\\socket.py', 'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Python\\Python310\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Python\\Python310\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Python\\Python310\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Python\\Python310\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Python\\Python310\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Python\\Python310\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Python\\Python310\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes', 'C:\\Python\\Python310\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Python\\Python310\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Python\\Python310\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Python\\Python310\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('queue', 'C:\\Python\\Python310\\lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Python\\Python310\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Python\\Python310\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('subprocess', 'C:\\Python\\Python310\\lib\\subprocess.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Python\\Python310\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy', 'C:\\Python\\Python310\\lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'C:\\Python\\Python310\\lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'C:\\Python\\Python310\\lib\\zipimport.py', 'PYMODULE'),
  ('_pyi_rth_utils.tempfile',
   'C:\\Python\\Python310\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\tempfile.py',
   'PYMODULE'),
  ('_pyi_rth_utils._win32',
   'C:\\Python\\Python310\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\_win32.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\Python\\Python310\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack',
   'C:\\Python\\Python310\\lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('setuptools',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('distutils.version',
   'C:\\Python\\Python310\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils',
   'C:\\Python\\Python310\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'C:\\Python\\Python310\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'C:\\Python\\Python310\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.file_util',
   'C:\\Python\\Python310\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'C:\\Python\\Python310\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.spawn',
   'C:\\Python\\Python310\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.debug',
   'C:\\Python\\Python310\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'C:\\Python\\Python310\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('_osx_support', 'C:\\Python\\Python310\\lib\\_osx_support.py', 'PYMODULE'),
  ('distutils.text_file',
   'C:\\Python\\Python310\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('sysconfig', 'C:\\Python\\Python310\\lib\\sysconfig.py', 'PYMODULE'),
  ('_aix_support', 'C:\\Python\\Python310\\lib\\_aix_support.py', 'PYMODULE'),
  ('_bootsubprocess',
   'C:\\Python\\Python310\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('distutils.log',
   'C:\\Python\\Python310\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'C:\\Python\\Python310\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'C:\\Python\\Python310\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\py39compat.py',
   'PYMODULE'),
  ('platform', 'C:\\Python\\Python310\\lib\\platform.py', 'PYMODULE'),
  ('setuptools._distutils.py38compat',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\msvccompiler.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'C:\\Python\\Python310\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'C:\\Python\\Python310\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('distutils.command',
   'C:\\Python\\Python310\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.cmd',
   'C:\\Python\\Python310\\lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.dist',
   'C:\\Python\\Python310\\lib\\distutils\\dist.py',
   'PYMODULE'),
  ('configparser', 'C:\\Python\\Python310\\lib\\configparser.py', 'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('distutils.unixccompiler',
   'C:\\Python\\Python310\\lib\\distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('distutils.extension',
   'C:\\Python\\Python310\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.config',
   'C:\\Python\\Python310\\lib\\distutils\\config.py',
   'PYMODULE'),
  ('cgi', 'C:\\Python\\Python310\\lib\\cgi.py', 'PYMODULE'),
  ('html', 'C:\\Python\\Python310\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'C:\\Python\\Python310\\lib\\html\\entities.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\upload.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.filelist',
   'C:\\Python\\Python310\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('glob', 'C:\\Python\\Python310\\lib\\glob.py', 'PYMODULE'),
  ('setuptools._distutils.command.register',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\register.py',
   'PYMODULE'),
  ('setuptools._distutils.command.py37compat',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\py37compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('site', 'C:\\Python\\Python310\\lib\\site.py', 'PYMODULE'),
  ('rlcompleter', 'C:\\Python\\Python310\\lib\\rlcompleter.py', 'PYMODULE'),
  ('_sitebuiltins', 'C:\\Python\\Python310\\lib\\_sitebuiltins.py', 'PYMODULE'),
  ('pydoc', 'C:\\Python\\Python310\\lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'C:\\Python\\Python310\\lib\\webbrowser.py', 'PYMODULE'),
  ('http.server', 'C:\\Python\\Python310\\lib\\http\\server.py', 'PYMODULE'),
  ('socketserver', 'C:\\Python\\Python310\\lib\\socketserver.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Python\\Python310\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Python\\Python310\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'C:\\Python\\Python310\\lib\\tty.py', 'PYMODULE'),
  ('setuptools._distutils.command.config',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'C:\\Python\\Python310\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Python\\Python310\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Python\\Python310\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Python\\Python310\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Python\\Python310\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Python\\Python310\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_wininst',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_wininst.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_msi',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_msi.py',
   'PYMODULE'),
  ('msilib.text', 'C:\\Python\\Python310\\lib\\msilib\\text.py', 'PYMODULE'),
  ('msilib.sequence',
   'C:\\Python\\Python310\\lib\\msilib\\sequence.py',
   'PYMODULE'),
  ('msilib.schema',
   'C:\\Python\\Python310\\lib\\msilib\\schema.py',
   'PYMODULE'),
  ('msilib', 'C:\\Python\\Python310\\lib\\msilib\\__init__.py', 'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Python\\Python310\\lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest', 'C:\\Python\\Python310\\lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest.async_case',
   'C:\\Python\\Python310\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Python\\Python310\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Python\\Python310\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Python\\Python310\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Python\\Python310\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Python\\Python310\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Python\\Python310\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Python\\Python310\\lib\\unittest\\_log.py',
   'PYMODULE'),
  ('difflib', 'C:\\Python\\Python310\\lib\\difflib.py', 'PYMODULE'),
  ('unittest.result',
   'C:\\Python\\Python310\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Python\\Python310\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('asyncio', 'C:\\Python\\Python310\\lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Python\\Python310\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log', 'C:\\Python\\Python310\\lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Python\\Python310\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Python\\Python310\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Python\\Python310\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Python\\Python310\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Python\\Python310\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Python\\Python310\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Python\\Python310\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Python\\Python310\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Python\\Python310\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Python\\Python310\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Python\\Python310\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Python\\Python310\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Python\\Python310\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Python\\Python310\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Python\\Python310\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Python\\Python310\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Python\\Python310\\lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Python\\Python310\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Python\\Python310\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Python\\Python310\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Python\\Python310\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Python\\Python310\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Python\\Python310\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Python\\Python310\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Python\\Python310\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Python\\Python310\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Python\\Python310\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\_collections.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\zipp.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('__future__', 'C:\\Python\\Python310\\lib\\__future__.py', 'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.util',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.unicode',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.testing',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.results',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.helpers',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.exceptions',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.core',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.diagram',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('jinja2',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2.ext',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.parser',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\Python\\Python310\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.tests',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.filters',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('markupsafe',
   'C:\\Python\\Python310\\lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'C:\\Python\\Python310\\lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('jinja2.utils',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.constants',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('json', 'C:\\Python\\Python310\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'C:\\Python\\Python310\\lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'C:\\Python\\Python310\\lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'C:\\Python\\Python310\\lib\\json\\scanner.py', 'PYMODULE'),
  ('jinja2.runtime',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.environment',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.debug',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('pkg_resources',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.metadata',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.util',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.unicode',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.testing',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.results',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.helpers',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.exceptions',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.core',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.diagram',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.common',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.actions',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Python\\Python310\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('imp', 'C:\\Python\\Python310\\lib\\imp.py', 'PYMODULE'),
  ('plistlib', 'C:\\Python\\Python310\\lib\\plistlib.py', 'PYMODULE'),
  ('pyparsing',
   'C:\\Python\\Python310\\lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.common',
   'C:\\Python\\Python310\\lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'C:\\Python\\Python310\\lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'C:\\Python\\Python310\\lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'C:\\Python\\Python310\\lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pyparsing.results',
   'C:\\Python\\Python310\\lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.core',
   'C:\\Python\\Python310\\lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'C:\\Python\\Python310\\lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'C:\\Python\\Python310\\lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'C:\\Python\\Python310\\lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.util',
   'C:\\Python\\Python310\\lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('pdb', 'C:\\Python\\Python310\\lib\\pdb.py', 'PYMODULE'),
  ('code', 'C:\\Python\\Python310\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Python\\Python310\\lib\\codeop.py', 'PYMODULE'),
  ('bdb', 'C:\\Python\\Python310\\lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'C:\\Python\\Python310\\lib\\cmd.py', 'PYMODULE'),
  ('setuptools._vendor.pyparsing.common',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.actions',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.__about__',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools.extern',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'C:\\Python\\Python310\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\py34compat.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'C:\\Python\\Python310\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.build_ext',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'C:\\Python\\Python310\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._deprecation_warning',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_deprecation_warning.py',
   'PYMODULE'),
  ('distutils.util',
   'C:\\Python\\Python310\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.errors',
   'C:\\Python\\Python310\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.core',
   'C:\\Python\\Python310\\lib\\distutils\\core.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Python\\Python310\\lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Python\\Python310\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Python\\Python310\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'C:\\Python\\Python310\\lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Python\\Python310\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Python\\Python310\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Python\\Python310\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('scipy.integrate',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\__init__.py',
   'PYMODULE'),
  ('scipy._lib._testutils',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_testutils.py',
   'PYMODULE'),
  ('scipy._lib._pep440',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_pep440.py',
   'PYMODULE'),
  ('scipy._lib',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.doccer',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\doccer.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_extra\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._funcs',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_extra\\_funcs.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._typing',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_extra\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._helpers',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_helpers.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask.array',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\array\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask.array._aliases',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\array\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask.array._info',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\array\\_info.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat._internal',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\_internal.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._typing',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._linalg',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_linalg.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.records',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._aliases',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.torch',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\torch\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.torch._aliases',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\torch\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.torch._info',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\torch\\_info.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy._aliases',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy._typing',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy._info',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\_info.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy.linalg',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\linalg.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy._aliases',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy._typing',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy._info',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\_info.py',
   'PYMODULE'),
  ('scipy.integrate.quadpack',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\quadpack.py',
   'PYMODULE'),
  ('scipy._lib.deprecation',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\deprecation.py',
   'PYMODULE'),
  ('scipy._lib._docscrape',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_docscrape.py',
   'PYMODULE'),
  ('scipy.integrate.odepack',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\odepack.py',
   'PYMODULE'),
  ('scipy.integrate.vode',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\vode.py',
   'PYMODULE'),
  ('scipy.integrate.lsoda',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\lsoda.py',
   'PYMODULE'),
  ('scipy.integrate.dop',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\dop.py',
   'PYMODULE'),
  ('scipy.integrate._lebedev',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_lebedev.py',
   'PYMODULE'),
  ('scipy.integrate._cubature',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_cubature.py',
   'PYMODULE'),
  ('scipy.integrate._rules._base',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_rules\\_base.py',
   'PYMODULE'),
  ('scipy.integrate._rules',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_rules\\__init__.py',
   'PYMODULE'),
  ('scipy.integrate._rules._gauss_legendre',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_rules\\_gauss_legendre.py',
   'PYMODULE'),
  ('scipy.special',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\__init__.py',
   'PYMODULE'),
  ('scipy.special.spfun_stats',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\spfun_stats.py',
   'PYMODULE'),
  ('scipy.special.sf_error',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\sf_error.py',
   'PYMODULE'),
  ('scipy.special.specfun',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\specfun.py',
   'PYMODULE'),
  ('scipy.special.orthogonal',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\orthogonal.py',
   'PYMODULE'),
  ('scipy.special.basic',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\basic.py',
   'PYMODULE'),
  ('scipy.special.add_newdocs',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\add_newdocs.py',
   'PYMODULE'),
  ('scipy.special._spherical_bessel',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_spherical_bessel.py',
   'PYMODULE'),
  ('scipy.special._lambertw',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_lambertw.py',
   'PYMODULE'),
  ('scipy.special._ellip_harm',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_ellip_harm.py',
   'PYMODULE'),
  ('scipy.special._spfun_stats',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_spfun_stats.py',
   'PYMODULE'),
  ('scipy.special._orthogonal',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_orthogonal.py',
   'PYMODULE'),
  ('scipy.special._multiufuncs',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_multiufuncs.py',
   'PYMODULE'),
  ('scipy.special._input_validation',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_input_validation.py',
   'PYMODULE'),
  ('scipy.special._logsumexp',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_logsumexp.py',
   'PYMODULE'),
  ('scipy.special._basic',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_basic.py',
   'PYMODULE'),
  ('scipy.special._support_alternative_backends',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_support_alternative_backends.py',
   'PYMODULE'),
  ('scipy.special._sf_error',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_sf_error.py',
   'PYMODULE'),
  ('scipy.integrate._rules._gauss_kronrod',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_rules\\_gauss_kronrod.py',
   'PYMODULE'),
  ('scipy.integrate._rules._genz_malik',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_rules\\_genz_malik.py',
   'PYMODULE'),
  ('scipy._lib._util',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_util.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('scipy._lib._array_api',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_array_api.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('scipy.integrate._tanhsinh',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_tanhsinh.py',
   'PYMODULE'),
  ('scipy._lib._elementwise_iterative_method',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_elementwise_iterative_method.py',
   'PYMODULE'),
  ('scipy.integrate._quad_vec',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_quad_vec.py',
   'PYMODULE'),
  ('scipy.integrate._ivp',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_ivp\\__init__.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.base',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_ivp\\base.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.common',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_ivp\\common.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.lsoda',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_ivp\\lsoda.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.bdf',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_ivp\\bdf.py',
   'PYMODULE'),
  ('scipy.optimize._numdiff',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_numdiff.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.radau',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_ivp\\radau.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.rk',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_ivp\\rk.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.ivp',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_ivp\\ivp.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.dop853_coefficients',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_ivp\\dop853_coefficients.py',
   'PYMODULE'),
  ('scipy.integrate._bvp',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_bvp.py',
   'PYMODULE'),
  ('scipy.interpolate',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\__init__.py',
   'PYMODULE'),
  ('scipy.interpolate.interpnd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\interpnd.py',
   'PYMODULE'),
  ('scipy.interpolate.rbf',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\rbf.py',
   'PYMODULE'),
  ('scipy.interpolate.polyint',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\polyint.py',
   'PYMODULE'),
  ('scipy.interpolate.ndgriddata',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\ndgriddata.py',
   'PYMODULE'),
  ('scipy.interpolate.interpolate',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\interpolate.py',
   'PYMODULE'),
  ('scipy.interpolate.fitpack2',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\fitpack2.py',
   'PYMODULE'),
  ('scipy.interpolate.fitpack',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\fitpack.py',
   'PYMODULE'),
  ('scipy.interpolate._bary_rational',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_bary_rational.py',
   'PYMODULE'),
  ('scipy.interpolate._ndbspline',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_ndbspline.py',
   'PYMODULE'),
  ('scipy.interpolate._rgi',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_rgi.py',
   'PYMODULE'),
  ('scipy.interpolate._pade',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_pade.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack_repro',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_fitpack_repro.py',
   'PYMODULE'),
  ('scipy.interpolate._bsplines',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_bsplines.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_fitpack_impl.py',
   'PYMODULE'),
  ('scipy.interpolate._ndgriddata',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_ndgriddata.py',
   'PYMODULE'),
  ('scipy.spatial',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\__init__.py',
   'PYMODULE'),
  ('scipy.spatial.transform',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\transform\\__init__.py',
   'PYMODULE'),
  ('scipy.spatial.transform.rotation',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\transform\\rotation.py',
   'PYMODULE'),
  ('scipy.spatial.transform._rotation_groups',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\transform\\_rotation_groups.py',
   'PYMODULE'),
  ('scipy.constants',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\constants\\__init__.py',
   'PYMODULE'),
  ('scipy.constants.constants',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\constants\\constants.py',
   'PYMODULE'),
  ('scipy.constants.codata',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\constants\\codata.py',
   'PYMODULE'),
  ('scipy.constants._constants',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\constants\\_constants.py',
   'PYMODULE'),
  ('scipy.constants._codata',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\constants\\_codata.py',
   'PYMODULE'),
  ('scipy.spatial.transform._rotation_spline',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\transform\\_rotation_spline.py',
   'PYMODULE'),
  ('scipy.spatial.distance',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\distance.py',
   'PYMODULE'),
  ('scipy.spatial.qhull',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\qhull.py',
   'PYMODULE'),
  ('scipy.spatial.kdtree',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\kdtree.py',
   'PYMODULE'),
  ('scipy.spatial.ckdtree',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\ckdtree.py',
   'PYMODULE'),
  ('scipy.spatial._geometric_slerp',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\_geometric_slerp.py',
   'PYMODULE'),
  ('scipy.spatial._procrustes',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\_procrustes.py',
   'PYMODULE'),
  ('scipy.spatial._plotutils',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\_plotutils.py',
   'PYMODULE'),
  ('matplotlib.collections',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\collections.py',
   'PYMODULE'),
  ('matplotlib.patheffects',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\patheffects.py',
   'PYMODULE'),
  ('matplotlib.patches',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\patches.py',
   'PYMODULE'),
  ('matplotlib.bezier',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\bezier.py',
   'PYMODULE'),
  ('matplotlib.backend_bases',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\backend_bases.py',
   'PYMODULE'),
  ('matplotlib.backends.registry',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\backends\\registry.py',
   'PYMODULE'),
  ('matplotlib.backends',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\backends\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends._backend_tk',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\backends\\_backend_tk.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL._typing',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys', 'C:\\Python\\Python310\\lib\\colorsys.py', 'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Python\\Python310\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Python\\Python310\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Python\\Python310\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Python\\Python310\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Python\\Python310\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'C:\\Python\\Python310\\lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.font', 'C:\\Python\\Python310\\lib\\tkinter\\font.py', 'PYMODULE'),
  ('uuid', 'C:\\Python\\Python310\\lib\\uuid.py', 'PYMODULE'),
  ('matplotlib.backends.backend_webagg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\backends\\backend_webagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg_core',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\backends\\backend_webagg_core.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_agg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\backends\\backend_agg.py',
   'PYMODULE'),
  ('matplotlib.mathtext',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mathtext.py',
   'PYMODULE'),
  ('matplotlib._mathtext',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_mathtext.py',
   'PYMODULE'),
  ('matplotlib._mathtext_data',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_mathtext_data.py',
   'PYMODULE'),
  ('matplotlib.font_manager',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\font_manager.py',
   'PYMODULE'),
  ('matplotlib.rcsetup',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\rcsetup.py',
   'PYMODULE'),
  ('cycler',
   'C:\\Python\\Python310\\lib\\site-packages\\cycler\\__init__.py',
   'PYMODULE'),
  ('matplotlib._fontconfig_pattern',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_fontconfig_pattern.py',
   'PYMODULE'),
  ('matplotlib._afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_afm.py',
   'PYMODULE'),
  ('matplotlib.texmanager',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\texmanager.py',
   'PYMODULE'),
  ('matplotlib.dviread',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\dviread.py',
   'PYMODULE'),
  ('matplotlib.layout_engine',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\layout_engine.py',
   'PYMODULE'),
  ('matplotlib._tight_layout',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_tight_layout.py',
   'PYMODULE'),
  ('matplotlib._constrained_layout',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_constrained_layout.py',
   'PYMODULE'),
  ('matplotlib._layoutgrid',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_layoutgrid.py',
   'PYMODULE'),
  ('kiwisolver',
   'C:\\Python\\Python310\\lib\\site-packages\\kiwisolver\\__init__.py',
   'PYMODULE'),
  ('kiwisolver.exceptions',
   'C:\\Python\\Python310\\lib\\site-packages\\kiwisolver\\exceptions.py',
   'PYMODULE'),
  ('matplotlib.backend_managers',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\backend_managers.py',
   'PYMODULE'),
  ('matplotlib._pylab_helpers',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_pylab_helpers.py',
   'PYMODULE'),
  ('matplotlib.widgets',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\widgets.py',
   'PYMODULE'),
  ('matplotlib.ticker',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\ticker.py',
   'PYMODULE'),
  ('matplotlib._tight_bbox',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_tight_bbox.py',
   'PYMODULE'),
  ('matplotlib.text',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\text.py',
   'PYMODULE'),
  ('matplotlib.offsetbox',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\offsetbox.py',
   'PYMODULE'),
  ('matplotlib.image',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\image.py',
   'PYMODULE'),
  ('matplotlib.projections',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\projections\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d',
   'C:\\Python\\Python310\\lib\\site-packages\\mpl_toolkits\\mplot3d\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits', '-', 'PYMODULE'),
  ('mpl_toolkits.mplot3d.axes3d',
   'C:\\Python\\Python310\\lib\\site-packages\\mpl_toolkits\\mplot3d\\axes3d.py',
   'PYMODULE'),
  ('matplotlib.tri._triangulation',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\tri\\_triangulation.py',
   'PYMODULE'),
  ('matplotlib.tri',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\tri\\__init__.py',
   'PYMODULE'),
  ('matplotlib.tri._tritools',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\tri\\_tritools.py',
   'PYMODULE'),
  ('matplotlib.tri._trirefine',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\tri\\_trirefine.py',
   'PYMODULE'),
  ('matplotlib.tri._triplot',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\tri\\_triplot.py',
   'PYMODULE'),
  ('matplotlib.tri._tripcolor',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\tri\\_tripcolor.py',
   'PYMODULE'),
  ('matplotlib.tri._triinterpolate',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\tri\\_triinterpolate.py',
   'PYMODULE'),
  ('matplotlib.tri._tricontour',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\tri\\_tricontour.py',
   'PYMODULE'),
  ('matplotlib.contour',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\contour.py',
   'PYMODULE'),
  ('contourpy',
   'C:\\Python\\Python310\\lib\\site-packages\\contourpy\\__init__.py',
   'PYMODULE'),
  ('contourpy.enum_util',
   'C:\\Python\\Python310\\lib\\site-packages\\contourpy\\enum_util.py',
   'PYMODULE'),
  ('contourpy.dechunk',
   'C:\\Python\\Python310\\lib\\site-packages\\contourpy\\dechunk.py',
   'PYMODULE'),
  ('contourpy.typecheck',
   'C:\\Python\\Python310\\lib\\site-packages\\contourpy\\typecheck.py',
   'PYMODULE'),
  ('contourpy.types',
   'C:\\Python\\Python310\\lib\\site-packages\\contourpy\\types.py',
   'PYMODULE'),
  ('contourpy.array',
   'C:\\Python\\Python310\\lib\\site-packages\\contourpy\\array.py',
   'PYMODULE'),
  ('contourpy.convert',
   'C:\\Python\\Python310\\lib\\site-packages\\contourpy\\convert.py',
   'PYMODULE'),
  ('contourpy.chunk',
   'C:\\Python\\Python310\\lib\\site-packages\\contourpy\\chunk.py',
   'PYMODULE'),
  ('contourpy._version',
   'C:\\Python\\Python310\\lib\\site-packages\\contourpy\\_version.py',
   'PYMODULE'),
  ('matplotlib.cm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\cm.py',
   'PYMODULE'),
  ('matplotlib._cm_bivar',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_cm_bivar.py',
   'PYMODULE'),
  ('matplotlib._cm_multivar',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_cm_multivar.py',
   'PYMODULE'),
  ('matplotlib._cm_listed',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_cm_listed.py',
   'PYMODULE'),
  ('matplotlib._cm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_cm.py',
   'PYMODULE'),
  ('matplotlib.tri._trifinder',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\tri\\_trifinder.py',
   'PYMODULE'),
  ('matplotlib.axes._base',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\axes\\_base.py',
   'PYMODULE'),
  ('matplotlib.table',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\table.py',
   'PYMODULE'),
  ('matplotlib.spines',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\spines.py',
   'PYMODULE'),
  ('matplotlib.gridspec',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\gridspec.py',
   'PYMODULE'),
  ('matplotlib.axis',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\axis.py',
   'PYMODULE'),
  ('dateutil.tz',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.easter',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil._common',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('six', 'C:\\Python\\Python310\\lib\\site-packages\\six.py', 'PYMODULE'),
  ('dateutil',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._version',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('matplotlib.units',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\units.py',
   'PYMODULE'),
  ('matplotlib.scale',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\scale.py',
   'PYMODULE'),
  ('matplotlib.container',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\container.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axis3d',
   'C:\\Python\\Python310\\lib\\site-packages\\mpl_toolkits\\mplot3d\\axis3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.art3d',
   'C:\\Python\\Python310\\lib\\site-packages\\mpl_toolkits\\mplot3d\\art3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.proj3d',
   'C:\\Python\\Python310\\lib\\site-packages\\mpl_toolkits\\mplot3d\\proj3d.py',
   'PYMODULE'),
  ('matplotlib.projections.polar',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\projections\\polar.py',
   'PYMODULE'),
  ('matplotlib.markers',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\markers.py',
   'PYMODULE'),
  ('matplotlib.projections.geo',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\projections\\geo.py',
   'PYMODULE'),
  ('matplotlib.axes',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\axes\\__init__.py',
   'PYMODULE'),
  ('matplotlib.axes._axes',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\axes\\_axes.py',
   'PYMODULE'),
  ('matplotlib.axes._secondary_axes',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\axes\\_secondary_axes.py',
   'PYMODULE'),
  ('matplotlib.streamplot',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\streamplot.py',
   'PYMODULE'),
  ('matplotlib.stackplot',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\stackplot.py',
   'PYMODULE'),
  ('matplotlib.quiver',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\quiver.py',
   'PYMODULE'),
  ('matplotlib.mlab',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mlab.py',
   'PYMODULE'),
  ('matplotlib.legend',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\legend.py',
   'PYMODULE'),
  ('matplotlib.legend_handler',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\legend_handler.py',
   'PYMODULE'),
  ('matplotlib.inset',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\inset.py',
   'PYMODULE'),
  ('matplotlib.dates',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\dates.py',
   'PYMODULE'),
  ('matplotlib.category',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\category.py',
   'PYMODULE'),
  ('matplotlib.textpath',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\textpath.py',
   'PYMODULE'),
  ('matplotlib._text_helpers',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_text_helpers.py',
   'PYMODULE'),
  ('matplotlib.backend_tools',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\backend_tools.py',
   'PYMODULE'),
  ('matplotlib._enums',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_enums.py',
   'PYMODULE'),
  ('matplotlib.transforms',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\transforms.py',
   'PYMODULE'),
  ('matplotlib.path',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\path.py',
   'PYMODULE'),
  ('matplotlib.lines',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\lines.py',
   'PYMODULE'),
  ('matplotlib.hatch',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\hatch.py',
   'PYMODULE'),
  ('matplotlib._docstring',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_docstring.py',
   'PYMODULE'),
  ('matplotlib.colors',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\colors.py',
   'PYMODULE'),
  ('matplotlib._color_data',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_color_data.py',
   'PYMODULE'),
  ('matplotlib.colorizer',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\colorizer.py',
   'PYMODULE'),
  ('matplotlib.cbook',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\cbook.py',
   'PYMODULE'),
  ('matplotlib.artist',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\artist.py',
   'PYMODULE'),
  ('matplotlib._api',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_api\\__init__.py',
   'PYMODULE'),
  ('matplotlib._api.deprecation',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_api\\deprecation.py',
   'PYMODULE'),
  ('matplotlib.pyplot',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\pyplot.py',
   'PYMODULE'),
  ('matplotlib.typing',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\typing.py',
   'PYMODULE'),
  ('matplotlib.style',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\style\\__init__.py',
   'PYMODULE'),
  ('matplotlib.style.core',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\style\\core.py',
   'PYMODULE'),
  ('matplotlib.colorbar',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\colorbar.py',
   'PYMODULE'),
  ('scipy._lib.decorator',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\decorator.py',
   'PYMODULE'),
  ('scipy.spatial._spherical_voronoi',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\_spherical_voronoi.py',
   'PYMODULE'),
  ('scipy.spatial._kdtree',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\_kdtree.py',
   'PYMODULE'),
  ('scipy.interpolate._cubic',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_cubic.py',
   'PYMODULE'),
  ('scipy.interpolate._polyint',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_polyint.py',
   'PYMODULE'),
  ('scipy.interpolate._rbfinterp',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_rbfinterp.py',
   'PYMODULE'),
  ('scipy.linalg.lapack',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\lapack.py',
   'PYMODULE'),
  ('scipy.linalg.blas',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\blas.py',
   'PYMODULE'),
  ('scipy.interpolate._rbf',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_rbf.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack2',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_fitpack2.py',
   'PYMODULE'),
  ('scipy.interpolate._interpolate',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_interpolate.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack_py',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_fitpack_py.py',
   'PYMODULE'),
  ('scipy.integrate._ode',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_ode.py',
   'PYMODULE'),
  ('scipy.integrate._quadpack_py',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_quadpack_py.py',
   'PYMODULE'),
  ('scipy.integrate._odepack_py',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_odepack_py.py',
   'PYMODULE'),
  ('scipy.integrate._quadrature',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_quadrature.py',
   'PYMODULE'),
  ('scipy.stats',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\__init__.py',
   'PYMODULE'),
  ('scipy.stats.stats',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\stats.py',
   'PYMODULE'),
  ('scipy.stats.mvn',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\mvn.py',
   'PYMODULE'),
  ('scipy.stats.mstats_extras',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\mstats_extras.py',
   'PYMODULE'),
  ('scipy.stats.mstats_basic',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\mstats_basic.py',
   'PYMODULE'),
  ('scipy.stats.morestats',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\morestats.py',
   'PYMODULE'),
  ('scipy.stats.kde',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\kde.py',
   'PYMODULE'),
  ('scipy.stats.biasedurn',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\biasedurn.py',
   'PYMODULE'),
  ('scipy.stats._correlation',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_correlation.py',
   'PYMODULE'),
  ('scipy.stats._axis_nan_policy',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_axis_nan_policy.py',
   'PYMODULE'),
  ('scipy.stats._mgc',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_mgc.py',
   'PYMODULE'),
  ('scipy.ndimage._measurements',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\_measurements.py',
   'PYMODULE'),
  ('scipy.ndimage._morphology',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\_morphology.py',
   'PYMODULE'),
  ('scipy.ndimage._filters',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\_filters.py',
   'PYMODULE'),
  ('scipy.ndimage._ni_docstrings',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\_ni_docstrings.py',
   'PYMODULE'),
  ('scipy.ndimage._ni_support',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\_ni_support.py',
   'PYMODULE'),
  ('scipy.ndimage',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\__init__.py',
   'PYMODULE'),
  ('scipy.ndimage.morphology',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\morphology.py',
   'PYMODULE'),
  ('scipy.ndimage.measurements',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\measurements.py',
   'PYMODULE'),
  ('scipy.ndimage.interpolation',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\interpolation.py',
   'PYMODULE'),
  ('scipy.ndimage.fourier',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\fourier.py',
   'PYMODULE'),
  ('scipy.ndimage.filters',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\filters.py',
   'PYMODULE'),
  ('scipy.ndimage._support_alternative_backends',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\_support_alternative_backends.py',
   'PYMODULE'),
  ('scipy.ndimage._ndimage_api',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\_ndimage_api.py',
   'PYMODULE'),
  ('scipy.ndimage._interpolation',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\_interpolation.py',
   'PYMODULE'),
  ('scipy.ndimage._fourier',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\_fourier.py',
   'PYMODULE'),
  ('scipy.ndimage._delegators',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\_delegators.py',
   'PYMODULE'),
  ('scipy._lib._bunch',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_bunch.py',
   'PYMODULE'),
  ('scipy.stats._new_distributions',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_new_distributions.py',
   'PYMODULE'),
  ('scipy.stats._distribution_infrastructure',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_distribution_infrastructure.py',
   'PYMODULE'),
  ('scipy.stats._probability_distribution',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_probability_distribution.py',
   'PYMODULE'),
  ('scipy.optimize._chandrupatla',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_chandrupatla.py',
   'PYMODULE'),
  ('scipy.optimize._bracket',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_bracket.py',
   'PYMODULE'),
  ('scipy.stats._survival',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_survival.py',
   'PYMODULE'),
  ('scipy.stats._common',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_common.py',
   'PYMODULE'),
  ('scipy.stats._sensitivity_analysis',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_sensitivity_analysis.py',
   'PYMODULE'),
  ('scipy.stats._qmc',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_qmc.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\csgraph\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph._laplacian',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\csgraph\\_laplacian.py',
   'PYMODULE'),
  ('scipy.sparse._sputils',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_sputils.py',
   'PYMODULE'),
  ('scipy.stats._fit',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_fit.py',
   'PYMODULE'),
  ('scipy.stats._bws_test',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_bws_test.py',
   'PYMODULE'),
  ('scipy.stats._mannwhitneyu',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_mannwhitneyu.py',
   'PYMODULE'),
  ('scipy.stats._page_trend_test',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_page_trend_test.py',
   'PYMODULE'),
  ('scipy.stats._hypotests',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_hypotests.py',
   'PYMODULE'),
  ('scipy.fft',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\__init__.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.helper',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\_pocketfft\\helper.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\_pocketfft\\__init__.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.realtransforms',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\_pocketfft\\realtransforms.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.basic',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\_pocketfft\\basic.py',
   'PYMODULE'),
  ('scipy.fft._backend',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\_backend.py',
   'PYMODULE'),
  ('scipy.fft._fftlog_backend',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\_fftlog_backend.py',
   'PYMODULE'),
  ('scipy._lib.uarray',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\uarray.py',
   'PYMODULE'),
  ('scipy._lib._uarray',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_uarray\\__init__.py',
   'PYMODULE'),
  ('scipy._lib._uarray._backend',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_uarray\\_backend.py',
   'PYMODULE'),
  ('scipy.fft._realtransforms_backend',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\_realtransforms_backend.py',
   'PYMODULE'),
  ('scipy.fft._basic_backend',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\_basic_backend.py',
   'PYMODULE'),
  ('scipy.fft._helper',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\_helper.py',
   'PYMODULE'),
  ('scipy.fft._fftlog',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\_fftlog.py',
   'PYMODULE'),
  ('scipy.fft._realtransforms',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\_realtransforms.py',
   'PYMODULE'),
  ('scipy.fft._basic',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\_basic.py',
   'PYMODULE'),
  ('scipy.stats._entropy',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_entropy.py',
   'PYMODULE'),
  ('scipy.stats._resampling',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_resampling.py',
   'PYMODULE'),
  ('scipy.stats._censored_data',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_censored_data.py',
   'PYMODULE'),
  ('scipy.stats.contingency',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\contingency.py',
   'PYMODULE'),
  ('scipy.stats._odds_ratio',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_odds_ratio.py',
   'PYMODULE'),
  ('scipy.stats._crosstab',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_crosstab.py',
   'PYMODULE'),
  ('scipy.stats._relative_risk',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_relative_risk.py',
   'PYMODULE'),
  ('scipy.stats._rcont',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_rcont\\__init__.py',
   'PYMODULE'),
  ('scipy.stats._covariance',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_covariance.py',
   'PYMODULE'),
  ('scipy.stats._multivariate',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_multivariate.py',
   'PYMODULE'),
  ('scipy.stats._qmvnt',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_qmvnt.py',
   'PYMODULE'),
  ('scipy.stats.mstats',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\mstats.py',
   'PYMODULE'),
  ('scipy.stats._mstats_extras',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_mstats_extras.py',
   'PYMODULE'),
  ('scipy.stats._kde',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_kde.py',
   'PYMODULE'),
  ('scipy.stats._binned_statistic',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_binned_statistic.py',
   'PYMODULE'),
  ('scipy.stats._binomtest',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_binomtest.py',
   'PYMODULE'),
  ('scipy.stats._multicomp',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_multicomp.py',
   'PYMODULE'),
  ('scipy.stats._wilcoxon',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_wilcoxon.py',
   'PYMODULE'),
  ('scipy.stats._morestats',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_morestats.py',
   'PYMODULE'),
  ('scipy.stats._distn_infrastructure',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_distn_infrastructure.py',
   'PYMODULE'),
  ('scipy.stats._constants',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_constants.py',
   'PYMODULE'),
  ('scipy._lib._finite_differences',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_finite_differences.py',
   'PYMODULE'),
  ('scipy.stats._distr_params',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_distr_params.py',
   'PYMODULE'),
  ('scipy.stats._variation',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_variation.py',
   'PYMODULE'),
  ('scipy.stats._stats_py',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_stats_py.py',
   'PYMODULE'),
  ('scipy.stats._stats_mstats_common',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_stats_mstats_common.py',
   'PYMODULE'),
  ('scipy.stats._mstats_basic',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_mstats_basic.py',
   'PYMODULE'),
  ('scipy.stats.distributions',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\distributions.py',
   'PYMODULE'),
  ('scipy.stats._levy_stable',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_levy_stable\\__init__.py',
   'PYMODULE'),
  ('scipy.stats._discrete_distns',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_discrete_distns.py',
   'PYMODULE'),
  ('scipy.stats._continuous_distns',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_continuous_distns.py',
   'PYMODULE'),
  ('scipy.stats._ksstats',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_ksstats.py',
   'PYMODULE'),
  ('scipy.stats._tukeylambda_stats',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_tukeylambda_stats.py',
   'PYMODULE'),
  ('scipy._lib._ccallback',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_ccallback.py',
   'PYMODULE'),
  ('cffi',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi.error',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.api',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('ctypes.util', 'C:\\Python\\Python310\\lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes._aix', 'C:\\Python\\Python310\\lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Python\\Python310\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Python\\Python310\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Python\\Python310\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Python\\Python310\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.verifier',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cffi.lock',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.cparser',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'C:\\Python\\Python310\\lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'C:\\Python\\Python310\\lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pycparser',
   'C:\\Python\\Python310\\lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'C:\\Python\\Python310\\lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'C:\\Python\\Python310\\lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'C:\\Python\\Python310\\lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'C:\\Python\\Python310\\lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'C:\\Python\\Python310\\lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'C:\\Python\\Python310\\lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.ply',
   'C:\\Python\\Python310\\lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'C:\\Python\\Python310\\lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.model',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('scipy.stats.qmc',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\qmc.py',
   'PYMODULE'),
  ('scipy.stats._warnings_errors',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_warnings_errors.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph._validation',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\csgraph\\_validation.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('doctest', 'C:\\Python\\Python310\\lib\\doctest.py', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('scipy',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\__init__.py',
   'PYMODULE'),
  ('scipy._distributor_init',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_distributor_init.py',
   'PYMODULE'),
  ('scipy.version',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\version.py',
   'PYMODULE'),
  ('scipy.__config__',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\__config__.py',
   'PYMODULE'),
  ('matplotlib',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\__init__.py',
   'PYMODULE'),
  ('certifi',
   'C:\\Python\\Python310\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Python\\Python310\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('matplotlib._blocking_input',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_blocking_input.py',
   'PYMODULE'),
  ('matplotlib._version',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_version.py',
   'PYMODULE'),
  ('tracemalloc', 'C:\\Python\\Python310\\lib\\tracemalloc.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Python\\Python310\\lib\\_py_abc.py', 'PYMODULE'),
  ('stringprep', 'C:\\Python\\Python310\\lib\\stringprep.py', 'PYMODULE'),
  ('scipy.sparse.linalg',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.matfuncs',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\matfuncs.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.eigen',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\eigen.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.interface',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\interface.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.dsolve',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\dsolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.isolve',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\isolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._special_sparse_arrays',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_special_sparse_arrays.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._expm_multiply',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_expm_multiply.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_qr',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_decomp_qr.py',
   'PYMODULE'),
  ('scipy.linalg._misc',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_misc.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._norm',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_norm.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._onenormest',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_onenormest.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._matfuncs',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_matfuncs.py',
   'PYMODULE'),
  ('scipy.sparse._construct',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_construct.py',
   'PYMODULE'),
  ('scipy.sparse._dia',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_dia.py',
   'PYMODULE'),
  ('scipy.sparse._data',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_data.py',
   'PYMODULE'),
  ('scipy.sparse._matrix',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_matrix.py',
   'PYMODULE'),
  ('scipy.sparse._lil',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_lil.py',
   'PYMODULE'),
  ('scipy.sparse._index',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_index.py',
   'PYMODULE'),
  ('scipy.sparse._dok',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_dok.py',
   'PYMODULE'),
  ('scipy.sparse._csr',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_csr.py',
   'PYMODULE'),
  ('scipy.sparse._spfuncs',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_spfuncs.py',
   'PYMODULE'),
  ('scipy.sparse._compressed',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_compressed.py',
   'PYMODULE'),
  ('scipy.sparse._csc',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_csc.py',
   'PYMODULE'),
  ('scipy.sparse._coo',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_coo.py',
   'PYMODULE'),
  ('scipy.sparse._bsr',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_bsr.py',
   'PYMODULE'),
  ('scipy.sparse._base',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_base.py',
   'PYMODULE'),
  ('scipy.linalg._basic',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_basic.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_svd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_decomp_svd.py',
   'PYMODULE'),
  ('scipy.linalg._decomp',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_decomp.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen._svds',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\_svds.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._svdp',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_svdp.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._propack', '-', 'PYMODULE'),
  ('scipy.sparse.linalg._eigen.lobpcg.lobpcg',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\lobpcg\\lobpcg.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.lobpcg',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\lobpcg\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.arpack',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.arpack.arpack',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\arpack.py',
   'PYMODULE'),
  ('scipy._lib._threadsafety',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_threadsafety.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._interface',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_interface.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve._add_newdocs',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\_add_newdocs.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve.linsolve',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\linsolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.tfqmr',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\tfqmr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.utils',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\utils.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve._gcrotmk',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\_gcrotmk.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lsmr',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lsmr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lsqr',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lsqr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lgmres',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lgmres.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.minres',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\minres.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.iterative',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\iterative.py',
   'PYMODULE'),
  ('scipy.sparse',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.sputils',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\sputils.py',
   'PYMODULE'),
  ('scipy.sparse.sparsetools',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\sparsetools.py',
   'PYMODULE'),
  ('scipy.sparse.lil',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\lil.py',
   'PYMODULE'),
  ('scipy.sparse.extract',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\extract.py',
   'PYMODULE'),
  ('scipy.sparse.dok',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\dok.py',
   'PYMODULE'),
  ('scipy.sparse.dia',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\dia.py',
   'PYMODULE'),
  ('scipy.sparse.data',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\data.py',
   'PYMODULE'),
  ('scipy.sparse.csr',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\csr.py',
   'PYMODULE'),
  ('scipy.sparse.csc',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\csc.py',
   'PYMODULE'),
  ('scipy.sparse.coo',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\coo.py',
   'PYMODULE'),
  ('scipy.sparse.construct',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\construct.py',
   'PYMODULE'),
  ('scipy.sparse.compressed',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\compressed.py',
   'PYMODULE'),
  ('scipy.sparse.bsr',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\bsr.py',
   'PYMODULE'),
  ('scipy.sparse.base',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\base.py',
   'PYMODULE'),
  ('scipy.sparse._matrix_io',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_matrix_io.py',
   'PYMODULE'),
  ('scipy.sparse._extract',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_extract.py',
   'PYMODULE'),
  ('numba',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\__init__.py',
   'PYMODULE'),
  ('numba.np.random.new_random_methods',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\random\\new_random_methods.py',
   'PYMODULE'),
  ('numba.np.random',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\random\\__init__.py',
   'PYMODULE'),
  ('numba.np.random.generator_methods',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\random\\generator_methods.py',
   'PYMODULE'),
  ('numba.np.random.distributions',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\random\\distributions.py',
   'PYMODULE'),
  ('numba.core.utils',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\utils.py',
   'PYMODULE'),
  ('numba.misc.dump_style',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\dump_style.py',
   'PYMODULE'),
  ('numba.misc',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\__init__.py',
   'PYMODULE'),
  ('numba.misc.literal',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\literal.py',
   'PYMODULE'),
  ('numba.misc.gdb_hook',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\gdb_hook.py',
   'PYMODULE'),
  ('numba.core.cgutils',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\cgutils.py',
   'PYMODULE'),
  ('numba.core.datamodel',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\datamodel\\__init__.py',
   'PYMODULE'),
  ('numba.core.datamodel.models',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\datamodel\\models.py',
   'PYMODULE'),
  ('numba.core.datamodel.registry',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\datamodel\\registry.py',
   'PYMODULE'),
  ('numba.core.datamodel.packer',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\datamodel\\packer.py',
   'PYMODULE'),
  ('numba.core.datamodel.manager',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\datamodel\\manager.py',
   'PYMODULE'),
  ('numba.core.debuginfo',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\debuginfo.py',
   'PYMODULE'),
  ('llvmlite.ir',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\ir\\__init__.py',
   'PYMODULE'),
  ('llvmlite.ir.transforms',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\ir\\transforms.py',
   'PYMODULE'),
  ('llvmlite.ir.builder',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\ir\\builder.py',
   'PYMODULE'),
  ('llvmlite.ir.instructions',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\ir\\instructions.py',
   'PYMODULE'),
  ('llvmlite.ir._utils',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\ir\\_utils.py',
   'PYMODULE'),
  ('llvmlite.ir.module',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\ir\\module.py',
   'PYMODULE'),
  ('llvmlite.ir.context',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\ir\\context.py',
   'PYMODULE'),
  ('llvmlite.ir.values',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\ir\\values.py',
   'PYMODULE'),
  ('llvmlite.ir.types',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\ir\\types.py',
   'PYMODULE'),
  ('numba.misc.cffiimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\cffiimpl.py',
   'PYMODULE'),
  ('numba.np.arrayobj',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\arrayobj.py',
   'PYMODULE'),
  ('numba.core.typing.npydecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\npydecl.py',
   'PYMODULE'),
  ('numba.core.typing.templates',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\templates.py',
   'PYMODULE'),
  ('numba.core.inline_closurecall',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\inline_closurecall.py',
   'PYMODULE'),
  ('numba.typed.listobject',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\typed\\listobject.py',
   'PYMODULE'),
  ('numba.typed.typedobjectutils',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\typed\\typedobjectutils.py',
   'PYMODULE'),
  ('numba.core.typeconv',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typeconv\\__init__.py',
   'PYMODULE'),
  ('numba.core.typeconv.rules',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typeconv\\rules.py',
   'PYMODULE'),
  ('numba.core.typeconv.typeconv',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typeconv\\typeconv.py',
   'PYMODULE'),
  ('numba.core.typeconv.castgraph',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typeconv\\castgraph.py',
   'PYMODULE'),
  ('numba.core.registry',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\registry.py',
   'PYMODULE'),
  ('numba.core.cpu',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\cpu.py',
   'PYMODULE'),
  ('numba.typed.dictobject',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\typed\\dictobject.py',
   'PYMODULE'),
  ('numba.np.unsafe',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\unsafe\\__init__.py',
   'PYMODULE'),
  ('numba.experimental.jitclass.base',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\experimental\\jitclass\\base.py',
   'PYMODULE'),
  ('numba.core.serialize',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\serialize.py',
   'PYMODULE'),
  ('numba.core.typing.asnumbatype',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\asnumbatype.py',
   'PYMODULE'),
  ('numba.core.typing.typeof',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\typeof.py',
   'PYMODULE'),
  ('numba.core.typing.ctypes_utils',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\ctypes_utils.py',
   'PYMODULE'),
  ('numba.core.typing.bufproto',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\bufproto.py',
   'PYMODULE'),
  ('numba.core.typing.cffi_utils',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\cffi_utils.py',
   'PYMODULE'),
  ('numba.cpython.randomimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\randomimpl.py',
   'PYMODULE'),
  ('numba.cpython.printimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\printimpl.py',
   'PYMODULE'),
  ('numba.cpython.mathimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\mathimpl.py',
   'PYMODULE'),
  ('numba.cpython.cmathimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\cmathimpl.py',
   'PYMODULE'),
  ('numba.np.npyimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\npyimpl.py',
   'PYMODULE'),
  ('numba.np.ufunc.sigparse',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\ufunc\\sigparse.py',
   'PYMODULE'),
  ('numba.np.npdatetime',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\npdatetime.py',
   'PYMODULE'),
  ('numba.extending',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\extending.py',
   'PYMODULE'),
  ('numba.np.npyfuncs',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\npyfuncs.py',
   'PYMODULE'),
  ('numba.np.math.numbers',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\math\\numbers.py',
   'PYMODULE'),
  ('numba.cpython.unsafe.numbers',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\unsafe\\numbers.py',
   'PYMODULE'),
  ('numba.cpython.unsafe',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\unsafe\\__init__.py',
   'PYMODULE'),
  ('numba.np.math.mathimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\math\\mathimpl.py',
   'PYMODULE'),
  ('numba.np.math.cmathimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\math\\cmathimpl.py',
   'PYMODULE'),
  ('numba.np.math',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\math\\__init__.py',
   'PYMODULE'),
  ('numba.core.lowering',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\lowering.py',
   'PYMODULE'),
  ('numba.core.pythonapi',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\pythonapi.py',
   'PYMODULE'),
  ('numba.core.boxing',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\boxing.py',
   'PYMODULE'),
  ('numba.core.unsafe.eh',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\unsafe\\eh.py',
   'PYMODULE'),
  ('numba.core.unsafe',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\unsafe\\__init__.py',
   'PYMODULE'),
  ('numba.misc.coverage_support',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\coverage_support.py',
   'PYMODULE'),
  ('numba.misc.firstlinefinder',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\firstlinefinder.py',
   'PYMODULE'),
  ('numba.core.environment',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\environment.py',
   'PYMODULE'),
  ('numba.core.removerefctpass',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\removerefctpass.py',
   'PYMODULE'),
  ('numba.core.generators',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\generators.py',
   'PYMODULE'),
  ('numba.core.funcdesc',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\funcdesc.py',
   'PYMODULE'),
  ('numba.core.itanium_mangler',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\itanium_mangler.py',
   'PYMODULE'),
  ('numba.np.npdatetime_helpers',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\npdatetime_helpers.py',
   'PYMODULE'),
  ('numba.experimental.function_type',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\experimental\\function_type.py',
   'PYMODULE'),
  ('numba.core.ccallback',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\ccallback.py',
   'PYMODULE'),
  ('numba.core.caching',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\caching.py',
   'PYMODULE'),
  ('numba.misc.appdirs',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\appdirs.py',
   'PYMODULE'),
  ('numba.experimental.jitclass',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\experimental\\jitclass\\__init__.py',
   'PYMODULE'),
  ('numba.experimental.jitclass.overloads',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\experimental\\jitclass\\overloads.py',
   'PYMODULE'),
  ('numba.experimental.jitclass.boxing',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\experimental\\jitclass\\boxing.py',
   'PYMODULE'),
  ('numba.experimental.jitclass.decorators',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\experimental\\jitclass\\decorators.py',
   'PYMODULE'),
  ('numba.typed.typedlist',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\typed\\typedlist.py',
   'PYMODULE'),
  ('numba.typed.dictimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\typed\\dictimpl.py',
   'PYMODULE'),
  ('numba.typed.typeddict',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\typed\\typeddict.py',
   'PYMODULE'),
  ('numba.np.polynomial.polynomial_functions',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\polynomial\\polynomial_functions.py',
   'PYMODULE'),
  ('numba.np.polynomial.polynomial_core',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\polynomial\\polynomial_core.py',
   'PYMODULE'),
  ('numba.np.polynomial',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numba.np.arraymath',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\arraymath.py',
   'PYMODULE'),
  ('numba.np.linalg',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\linalg.py',
   'PYMODULE'),
  ('numba.core.optional',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\optional.py',
   'PYMODULE'),
  ('numba.cpython.unicode',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\unicode.py',
   'PYMODULE'),
  ('numba.cpython.unicode_support',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\unicode_support.py',
   'PYMODULE'),
  ('numba.core.unsafe.bytes',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\unsafe\\bytes.py',
   'PYMODULE'),
  ('numba.cpython.tupleobj',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\tupleobj.py',
   'PYMODULE'),
  ('numba.cpython.setobj',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\setobj.py',
   'PYMODULE'),
  ('numba.cpython.numbers',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\numbers.py',
   'PYMODULE'),
  ('numba.cpython.iterators',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\iterators.py',
   'PYMODULE'),
  ('numba.cpython.heapq',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\heapq.py',
   'PYMODULE'),
  ('numba.cpython.hashing',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\hashing.py',
   'PYMODULE'),
  ('numba.cpython.enumimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\enumimpl.py',
   'PYMODULE'),
  ('numba.cpython.charseq',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\charseq.py',
   'PYMODULE'),
  ('numba.cpython.builtins',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\builtins.py',
   'PYMODULE'),
  ('numba.np.ufunc_db',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\ufunc_db.py',
   'PYMODULE'),
  ('numba.core.entrypoints',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\entrypoints.py',
   'PYMODULE'),
  ('numba.core.compiler_lock',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\compiler_lock.py',
   'PYMODULE'),
  ('numba.core.event',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\event.py',
   'PYMODULE'),
  ('numba.core.runtime',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\runtime\\__init__.py',
   'PYMODULE'),
  ('numba.core.runtime.nrt',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\runtime\\nrt.py',
   'PYMODULE'),
  ('numba.core.runtime.nrtdynmod',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\runtime\\nrtdynmod.py',
   'PYMODULE'),
  ('numba.core.options',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\options.py',
   'PYMODULE'),
  ('numba.core.intrinsics',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\intrinsics.py',
   'PYMODULE'),
  ('numba.core.fastmathpass',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\fastmathpass.py',
   'PYMODULE'),
  ('numba.core.externals',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\externals.py',
   'PYMODULE'),
  ('numba.core.codegen',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\codegen.py',
   'PYMODULE'),
  ('numba.misc.llvm_pass_timings',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\llvm_pass_timings.py',
   'PYMODULE'),
  ('numba.misc.inspection',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\inspection.py',
   'PYMODULE'),
  ('numba.pycc.platform',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\pycc\\platform.py',
   'PYMODULE'),
  ('numba.pycc',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\pycc\\__init__.py',
   'PYMODULE'),
  ('numba.pycc.decorators',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\pycc\\decorators.py',
   'PYMODULE'),
  ('numba.pycc.compiler',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\pycc\\compiler.py',
   'PYMODULE'),
  ('numba.core.sigutils',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\sigutils.py',
   'PYMODULE'),
  ('numba.pycc.cc',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\pycc\\cc.py',
   'PYMODULE'),
  ('numba.pycc.llvm_types',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\pycc\\llvm_types.py',
   'PYMODULE'),
  ('numba.core.runtime.nrtopt',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\runtime\\nrtopt.py',
   'PYMODULE'),
  ('numba.core.llvm_bindings',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\llvm_bindings.py',
   'PYMODULE'),
  ('numba.core.callconv',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\callconv.py',
   'PYMODULE'),
  ('numba.core.base',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\base.py',
   'PYMODULE'),
  ('numba.core.runtime.context',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\runtime\\context.py',
   'PYMODULE'),
  ('numba.core.callwrapper',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\callwrapper.py',
   'PYMODULE'),
  ('numba.core.dispatcher',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\dispatcher.py',
   'PYMODULE'),
  ('numba.core.annotations.pretty_annotate',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\annotations\\pretty_annotate.py',
   'PYMODULE'),
  ('numba.core.annotations',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\annotations\\__init__.py',
   'PYMODULE'),
  ('numba.core.annotations.type_annotations',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\annotations\\type_annotations.py',
   'PYMODULE'),
  ('numba.core.descriptors',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\descriptors.py',
   'PYMODULE'),
  ('numba.core.ssa',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\ssa.py',
   'PYMODULE'),
  ('numba.core.bytecode',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\bytecode.py',
   'PYMODULE'),
  ('numba.core.untyped_passes',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\untyped_passes.py',
   'PYMODULE'),
  ('numba.core.interpreter',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\interpreter.py',
   'PYMODULE'),
  ('numba.core.byteflow',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\byteflow.py',
   'PYMODULE'),
  ('numba.core.controlflow',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\controlflow.py',
   'PYMODULE'),
  ('numba.core.consts',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\consts.py',
   'PYMODULE'),
  ('numba.core.transforms',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\transforms.py',
   'PYMODULE'),
  ('numba.core.compiler_machinery',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\compiler_machinery.py',
   'PYMODULE'),
  ('numba.core.tracing',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\tracing.py',
   'PYMODULE'),
  ('numba.stencils.stencil',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\stencils\\stencil.py',
   'PYMODULE'),
  ('numba.stencils',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\stencils\\__init__.py',
   'PYMODULE'),
  ('numba.np.unsafe.ndarray',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\unsafe\\ndarray.py',
   'PYMODULE'),
  ('numba.core.postproc',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\postproc.py',
   'PYMODULE'),
  ('numba.cpython.rangeobj',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\rangeobj.py',
   'PYMODULE'),
  ('numba.cpython.listobj',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\listobj.py',
   'PYMODULE'),
  ('numba.parfors.parfor',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\parfors\\parfor.py',
   'PYMODULE'),
  ('numba.parfors.array_analysis',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\parfors\\array_analysis.py',
   'PYMODULE'),
  ('numba.core.types.functions',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\functions.py',
   'PYMODULE'),
  ('numba.core.types.misc',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\misc.py',
   'PYMODULE'),
  ('numba.core.types.common',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\common.py',
   'PYMODULE'),
  ('numba.core.types.iterators',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\iterators.py',
   'PYMODULE'),
  ('numba.core.types.abstract',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\abstract.py',
   'PYMODULE'),
  ('numba.stencils.stencilparfor',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\stencils\\stencilparfor.py',
   'PYMODULE'),
  ('numba.parfors',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\parfors\\__init__.py',
   'PYMODULE'),
  ('numba.parfors.parfor_lowering',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\parfors\\parfor_lowering.py',
   'PYMODULE'),
  ('numba.np.ufunc.parallel',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\ufunc\\parallel.py',
   'PYMODULE'),
  ('numba.np.ufunc.ufuncbuilder',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\ufunc\\ufuncbuilder.py',
   'PYMODULE'),
  ('numba.np.ufunc.wrappers',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\ufunc\\wrappers.py',
   'PYMODULE'),
  ('numba.parfors.parfor_lowering_utils',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\parfors\\parfor_lowering_utils.py',
   'PYMODULE'),
  ('numba.core.typeinfer',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typeinfer.py',
   'PYMODULE'),
  ('numba.core.ir_utils',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\ir_utils.py',
   'PYMODULE'),
  ('numba.core.rewrites',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\rewrites\\__init__.py',
   'PYMODULE'),
  ('numba.core.rewrites.ir_print',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\rewrites\\ir_print.py',
   'PYMODULE'),
  ('numba.core.rewrites.static_binop',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\rewrites\\static_binop.py',
   'PYMODULE'),
  ('numba.core.rewrites.static_raise',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\rewrites\\static_raise.py',
   'PYMODULE'),
  ('numba.core.rewrites.static_getitem',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\rewrites\\static_getitem.py',
   'PYMODULE'),
  ('numba.core.rewrites.registry',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\rewrites\\registry.py',
   'PYMODULE'),
  ('numba.core.ir',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\ir.py',
   'PYMODULE'),
  ('numba.core.analysis',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\analysis.py',
   'PYMODULE'),
  ('numba.core.compiler',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\compiler.py',
   'PYMODULE'),
  ('numba.core.object_mode_passes',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\object_mode_passes.py',
   'PYMODULE'),
  ('numba.core.pylowering',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\pylowering.py',
   'PYMODULE'),
  ('numba.core.typed_passes',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typed_passes.py',
   'PYMODULE'),
  ('numba.core.cpu_options',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\cpu_options.py',
   'PYMODULE'),
  ('numba.core.targetconfig',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\targetconfig.py',
   'PYMODULE'),
  ('numba.cpython.unsafe.tuple',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\unsafe\\tuple.py',
   'PYMODULE'),
  ('numba.cpython.slicing',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\slicing.py',
   'PYMODULE'),
  ('numba.cpython',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\__init__.py',
   'PYMODULE'),
  ('numba.core.typing',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\__init__.py',
   'PYMODULE'),
  ('numba.core.typing.context',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\context.py',
   'PYMODULE'),
  ('numba.core.typing.dictdecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\dictdecl.py',
   'PYMODULE'),
  ('numba.core.typing.setdecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\setdecl.py',
   'PYMODULE'),
  ('numba.core.typing.mathdecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\mathdecl.py',
   'PYMODULE'),
  ('numba.core.typing.listdecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\listdecl.py',
   'PYMODULE'),
  ('numba.core.typing.npdatetime',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\npdatetime.py',
   'PYMODULE'),
  ('numba.core.typing.arraydecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\arraydecl.py',
   'PYMODULE'),
  ('numba.core.typing.collections',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\collections.py',
   'PYMODULE'),
  ('numba.core.typing.builtins',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\builtins.py',
   'PYMODULE'),
  ('numba.core.typing.enumdecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\enumdecl.py',
   'PYMODULE'),
  ('numba.core.typing.cmathdecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\cmathdecl.py',
   'PYMODULE'),
  ('numba.core.imputils',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\imputils.py',
   'PYMODULE'),
  ('numba.misc.mergesort',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\mergesort.py',
   'PYMODULE'),
  ('numba.misc.quicksort',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\quicksort.py',
   'PYMODULE'),
  ('timeit', 'C:\\Python\\Python310\\lib\\timeit.py', 'PYMODULE'),
  ('numba.core.types.containers',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\containers.py',
   'PYMODULE'),
  ('numba.np.random.random_methods',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\random\\random_methods.py',
   'PYMODULE'),
  ('numba.np',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\__init__.py',
   'PYMODULE'),
  ('numba.np.random.generator_core',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\random\\generator_core.py',
   'PYMODULE'),
  ('numba.np.random._constants',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\random\\_constants.py',
   'PYMODULE'),
  ('numba.core.extending',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\extending.py',
   'PYMODULE'),
  ('numba.np.random.new_distributions',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\random\\new_distributions.py',
   'PYMODULE'),
  ('numba.np.new_arraymath',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\new_arraymath.py',
   'PYMODULE'),
  ('numba.cpython.new_tupleobj',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\new_tupleobj.py',
   'PYMODULE'),
  ('numba.cpython.new_numbers',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\new_numbers.py',
   'PYMODULE'),
  ('numba.cpython.new_mathimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\new_mathimpl.py',
   'PYMODULE'),
  ('numba.cpython.new_hashing',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\new_hashing.py',
   'PYMODULE'),
  ('numba.cpython.new_builtins',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\new_builtins.py',
   'PYMODULE'),
  ('numba.core.typing.new_mathdecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\new_mathdecl.py',
   'PYMODULE'),
  ('numba.core.typing.new_cmathdecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\new_cmathdecl.py',
   'PYMODULE'),
  ('numba.core.typing.new_builtins',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\new_builtins.py',
   'PYMODULE'),
  ('numba.core.types.new_scalars',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\new_scalars\\__init__.py',
   'PYMODULE'),
  ('numba.core.types.new_scalars.numpy_types',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\new_scalars\\numpy_types.py',
   'PYMODULE'),
  ('numba.core.types.new_scalars.machine_types',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\new_scalars\\machine_types.py',
   'PYMODULE'),
  ('numba.core.types.new_scalars.python_types',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\new_scalars\\python_types.py',
   'PYMODULE'),
  ('numba.core.types.new_scalars.scalars',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\new_scalars\\scalars.py',
   'PYMODULE'),
  ('numba.core.new_boxing',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\new_boxing.py',
   'PYMODULE'),
  ('numba.core.datamodel.new_models',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\datamodel\\new_models.py',
   'PYMODULE'),
  ('numba.np.random.old_random_methods',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\random\\old_random_methods.py',
   'PYMODULE'),
  ('numba.np.random.old_distributions',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\random\\old_distributions.py',
   'PYMODULE'),
  ('numba.np.old_arraymath',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\old_arraymath.py',
   'PYMODULE'),
  ('numba.cpython.old_tupleobj',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\old_tupleobj.py',
   'PYMODULE'),
  ('numba.cpython.old_numbers',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\old_numbers.py',
   'PYMODULE'),
  ('numba.cpython.old_mathimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\old_mathimpl.py',
   'PYMODULE'),
  ('numba.cpython.old_hashing',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\old_hashing.py',
   'PYMODULE'),
  ('numba.cpython.old_builtins',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\old_builtins.py',
   'PYMODULE'),
  ('numba.core.typing.old_mathdecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\old_mathdecl.py',
   'PYMODULE'),
  ('numba.core.typing.old_cmathdecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\old_cmathdecl.py',
   'PYMODULE'),
  ('numba.core.typing.old_builtins',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\old_builtins.py',
   'PYMODULE'),
  ('numba.core.types.old_scalars',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\old_scalars.py',
   'PYMODULE'),
  ('numba.core.old_boxing',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\old_boxing.py',
   'PYMODULE'),
  ('numba.core.datamodel.old_models',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\datamodel\\old_models.py',
   'PYMODULE'),
  ('numba.cloudpickle.cloudpickle_fast',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cloudpickle\\cloudpickle_fast.py',
   'PYMODULE'),
  ('numba.cloudpickle.cloudpickle',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cloudpickle\\cloudpickle.py',
   'PYMODULE'),
  ('llvmlite.binding',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\__init__.py',
   'PYMODULE'),
  ('llvmlite.binding.orcjit',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\orcjit.py',
   'PYMODULE'),
  ('llvmlite.binding.context',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\context.py',
   'PYMODULE'),
  ('llvmlite.binding.analysis',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\analysis.py',
   'PYMODULE'),
  ('llvmlite.binding.typeref',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\typeref.py',
   'PYMODULE'),
  ('llvmlite.binding.value',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\value.py',
   'PYMODULE'),
  ('llvmlite.binding.common',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\common.py',
   'PYMODULE'),
  ('llvmlite.binding.transforms',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\transforms.py',
   'PYMODULE'),
  ('llvmlite.binding.passmanagers',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\passmanagers.py',
   'PYMODULE'),
  ('llvmlite.binding.newpassmanagers',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\newpassmanagers.py',
   'PYMODULE'),
  ('llvmlite.binding.options',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\options.py',
   'PYMODULE'),
  ('llvmlite.binding.module',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\module.py',
   'PYMODULE'),
  ('llvmlite.binding.linker',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\linker.py',
   'PYMODULE'),
  ('llvmlite.binding.initfini',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\initfini.py',
   'PYMODULE'),
  ('llvmlite.binding.executionengine',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\executionengine.py',
   'PYMODULE'),
  ('llvmlite.binding.object_file',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\object_file.py',
   'PYMODULE'),
  ('llvmlite.binding.targets',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\targets.py',
   'PYMODULE'),
  ('llvmlite.binding.dylib',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\dylib.py',
   'PYMODULE'),
  ('llvmlite.binding.ffi',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\ffi.py',
   'PYMODULE'),
  ('llvmlite.utils',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\utils.py',
   'PYMODULE'),
  ('llvmlite',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\__init__.py',
   'PYMODULE'),
  ('llvmlite._version',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\_version.py',
   'PYMODULE'),
  ('numba.testing._runtests',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\testing\\_runtests.py',
   'PYMODULE'),
  ('numba.tests.support',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\tests\\support.py',
   'PYMODULE'),
  ('numba.tests',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\tests\\__init__.py',
   'PYMODULE'),
  ('numba.testing',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\testing\\__init__.py',
   'PYMODULE'),
  ('numba.testing.main',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\testing\\main.py',
   'PYMODULE'),
  ('numba.cuda.testing',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\testing.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.nvvm',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\nvvm.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.runtime',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\runtime.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.enums',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\enums.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.rtapi',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\rtapi.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.error',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\error.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.libs',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\libs.py',
   'PYMODULE'),
  ('numba.misc.findlib',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\findlib.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.devices',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\devices.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.driver',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\driver.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.devicearray',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\devicearray.py',
   'PYMODULE'),
  ('numba.cuda.kernels.transpose',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\kernels\\transpose.py',
   'PYMODULE'),
  ('numba.cuda.kernels',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\kernels\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.kernels.reduction',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\kernels\\reduction.py',
   'PYMODULE'),
  ('numba.cuda.api_util',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\api_util.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.dummyarray',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\dummyarray.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.nvrtc',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\nvrtc.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.drvapi',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\drvapi.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.cuda_paths',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cuda_paths.py',
   'PYMODULE'),
  ('numba.testing.loader',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\testing\\loader.py',
   'PYMODULE'),
  ('cProfile', 'C:\\Python\\Python310\\lib\\cProfile.py', 'PYMODULE'),
  ('pstats', 'C:\\Python\\Python310\\lib\\pstats.py', 'PYMODULE'),
  ('profile', 'C:\\Python\\Python310\\lib\\profile.py', 'PYMODULE'),
  ('numba.typed',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\typed\\__init__.py',
   'PYMODULE'),
  ('numba.core.target_extension',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\target_extension.py',
   'PYMODULE'),
  ('numba.core.withcontexts',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\withcontexts.py',
   'PYMODULE'),
  ('numba.experimental',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\experimental\\__init__.py',
   'PYMODULE'),
  ('numba.np.numpy_support',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\numpy_support.py',
   'PYMODULE'),
  ('numba.np.ufunc',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\ufunc\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.vectorizers',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\vectorizers.py',
   'PYMODULE'),
  ('numba.cuda.deviceufunc',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\deviceufunc.py',
   'PYMODULE'),
  ('numba.np.ufunc.array_exprs',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\ufunc\\array_exprs.py',
   'PYMODULE'),
  ('numba.np.ufunc.decorators',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\ufunc\\decorators.py',
   'PYMODULE'),
  ('numba.np.ufunc.gufunc',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\ufunc\\gufunc.py',
   'PYMODULE'),
  ('numba.np.ufunc.ufunc_base',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\ufunc\\ufunc_base.py',
   'PYMODULE'),
  ('numba.np.ufunc.dufunc',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\ufunc\\dufunc.py',
   'PYMODULE'),
  ('numba.core.decorators',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\decorators.py',
   'PYMODULE'),
  ('numba.misc.special',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\special.py',
   'PYMODULE'),
  ('numba.core.errors',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\errors.py',
   'PYMODULE'),
  ('colorama',
   'C:\\Python\\Python310\\lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'C:\\Python\\Python310\\lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'C:\\Python\\Python310\\lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorama.ansi',
   'C:\\Python\\Python310\\lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.initialise',
   'C:\\Python\\Python310\\lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'C:\\Python\\Python310\\lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('numba.core.types',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\__init__.py',
   'PYMODULE'),
  ('numba.core.types.function_type',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\function_type.py',
   'PYMODULE'),
  ('numba.core.types.scalars',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\scalars.py',
   'PYMODULE'),
  ('numba.core.types.npytypes',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\npytypes.py',
   'PYMODULE'),
  ('numba.core.config',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\config.py',
   'PYMODULE'),
  ('numba.cuda',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.compiler',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\compiler.py',
   'PYMODULE'),
  ('numba.cuda.descriptor',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\descriptor.py',
   'PYMODULE'),
  ('numba.cuda.target',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\target.py',
   'PYMODULE'),
  ('numba.cuda.mathimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\mathimpl.py',
   'PYMODULE'),
  ('numba.cuda.dispatcher',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\dispatcher.py',
   'PYMODULE'),
  ('numba.cuda.types',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\types.py',
   'PYMODULE'),
  ('numba.cuda.errors',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\errors.py',
   'PYMODULE'),
  ('numba.cuda.args',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\args.py',
   'PYMODULE'),
  ('numba.cuda.models',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\models.py',
   'PYMODULE'),
  ('numba.cuda.api',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\api.py',
   'PYMODULE'),
  ('numba.cuda.device_init',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\device_init.py',
   'PYMODULE'),
  ('numba.cuda.intrinsic_wrapper',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\intrinsic_wrapper.py',
   'PYMODULE'),
  ('numba.cuda.decorators',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\decorators.py',
   'PYMODULE'),
  ('numba.cuda.simulator.kernel',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\kernel.py',
   'PYMODULE'),
  ('numba.cuda.simulator',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.simulator.compiler',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\compiler.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.runtime',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\runtime.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.devices',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\devices.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.nvvm',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\nvvm.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.error',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\error.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.drvapi',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\drvapi.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.driver',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\driver.py',
   'PYMODULE'),
  ('numba.cuda.simulator.reduction',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\reduction.py',
   'PYMODULE'),
  ('numba.cuda.simulator.vector_types',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\vector_types.py',
   'PYMODULE'),
  ('numba.cuda.simulator.api',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\api.py',
   'PYMODULE'),
  ('numba.cuda.simulator.kernelapi',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\kernelapi.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.devicearray',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\devicearray.py',
   'PYMODULE'),
  ('numba.cuda.intrinsics',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\intrinsics.py',
   'PYMODULE'),
  ('numba.cuda.extending',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\extending.py',
   'PYMODULE'),
  ('numba.cuda.stubs',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\stubs.py',
   'PYMODULE'),
  ('numba.cuda.initialize',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\initialize.py',
   'PYMODULE'),
  ('numba.cuda.libdeviceimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\libdeviceimpl.py',
   'PYMODULE'),
  ('numba.cuda.printimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\printimpl.py',
   'PYMODULE'),
  ('numba.cuda.cudaimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudaimpl.py',
   'PYMODULE'),
  ('numba.cuda.vector_types',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\vector_types.py',
   'PYMODULE'),
  ('numba.cuda.libdevicedecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\libdevicedecl.py',
   'PYMODULE'),
  ('numba.cuda.libdevicefuncs',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\libdevicefuncs.py',
   'PYMODULE'),
  ('numba.cuda.cudamath',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudamath.py',
   'PYMODULE'),
  ('numba.cuda.cudadecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadecl.py',
   'PYMODULE'),
  ('numba.cuda.ufuncs',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\ufuncs.py',
   'PYMODULE'),
  ('numba.cuda.libdevice',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\libdevice.py',
   'PYMODULE'),
  ('numba.cuda.codegen',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\codegen.py',
   'PYMODULE'),
  ('numba.cuda.cg',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cg.py',
   'PYMODULE'),
  ('numba.cuda.nvvmutils',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\nvvmutils.py',
   'PYMODULE'),
  ('numba.cuda.simulator_init',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator_init.py',
   'PYMODULE'),
  ('numba.runtests',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\runtests.py',
   'PYMODULE'),
  ('numba.types',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\types\\__init__.py',
   'PYMODULE'),
  ('numba.cext',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cext\\__init__.py',
   'PYMODULE'),
  ('numba.cloudpickle',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cloudpickle\\__init__.py',
   'PYMODULE'),
  ('numba.core',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\__init__.py',
   'PYMODULE'),
  ('numba.misc.init_utils',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\init_utils.py',
   'PYMODULE'),
  ('numba._version',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\_version.py',
   'PYMODULE'),
  ('scipy.optimize',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize.zeros',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\zeros.py',
   'PYMODULE'),
  ('scipy.optimize.tnc',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\tnc.py',
   'PYMODULE'),
  ('scipy.optimize.slsqp',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\slsqp.py',
   'PYMODULE'),
  ('scipy.optimize.optimize',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\optimize.py',
   'PYMODULE'),
  ('scipy.optimize.nonlin',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\nonlin.py',
   'PYMODULE'),
  ('scipy.optimize.moduleTNC',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\moduleTNC.py',
   'PYMODULE'),
  ('scipy.optimize.minpack2',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\minpack2.py',
   'PYMODULE'),
  ('scipy.optimize.minpack',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\minpack.py',
   'PYMODULE'),
  ('scipy.optimize.linesearch',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\linesearch.py',
   'PYMODULE'),
  ('scipy.optimize.lbfgsb',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\lbfgsb.py',
   'PYMODULE'),
  ('scipy.optimize.cobyla',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\cobyla.py',
   'PYMODULE'),
  ('scipy.optimize._milp',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_milp.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_highs',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_linprog_highs.py',
   'PYMODULE'),
  ('scipy.optimize._highspy',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_highspy\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._highspy._highs_wrapper',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_highspy\\_highs_wrapper.py',
   'PYMODULE'),
  ('scipy.optimize._direct_py',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_direct_py.py',
   'PYMODULE'),
  ('scipy.optimize._qap',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_qap.py',
   'PYMODULE'),
  ('scipy.optimize._dual_annealing',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_dual_annealing.py',
   'PYMODULE'),
  ('scipy.optimize._shgo',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_shgo.py',
   'PYMODULE'),
  ('scipy.optimize._shgo_lib._complex',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_shgo_lib\\_complex.py',
   'PYMODULE'),
  ('scipy.optimize._shgo_lib',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_shgo_lib\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._shgo_lib._vertex',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_shgo_lib\\_vertex.py',
   'PYMODULE'),
  ('scipy.optimize._hessian_update_strategy',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_hessian_update_strategy.py',
   'PYMODULE'),
  ('scipy.optimize._constraints',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_constraints.py',
   'PYMODULE'),
  ('scipy.optimize._differentiable_functions',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_differentiable_functions.py',
   'PYMODULE'),
  ('scipy.optimize._isotonic',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_isotonic.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.lsq_linear',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_lsq\\lsq_linear.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.bvls',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_lsq\\bvls.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.trf_linear',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_lsq\\trf_linear.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.common',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_lsq\\common.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.least_squares',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_lsq\\least_squares.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.dogbox',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_lsq\\dogbox.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.trf',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_lsq\\trf.py',
   'PYMODULE'),
  ('scipy.optimize._lsq',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_lsq\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._differentialevolution',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_differentialevolution.py',
   'PYMODULE'),
  ('scipy.optimize._linprog',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_linprog.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_util',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_linprog_util.py',
   'PYMODULE'),
  ('scipy.optimize._remove_redundancy',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_remove_redundancy.py',
   'PYMODULE'),
  ('scipy.linalg.interpolative',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\interpolative.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_doc',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_linprog_doc.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_rs',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_linprog_rs.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_simplex',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_linprog_simplex.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_ip',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_linprog_ip.py',
   'PYMODULE'),
  ('scipy.optimize._basinhopping',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_basinhopping.py',
   'PYMODULE'),
  ('scipy.optimize._nnls',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_nnls.py',
   'PYMODULE'),
  ('scipy.optimize._slsqp_py',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_slsqp_py.py',
   'PYMODULE'),
  ('scipy.optimize._cobyla_py',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_cobyla_py.py',
   'PYMODULE'),
  ('scipy.optimize._tnc',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_tnc.py',
   'PYMODULE'),
  ('scipy.optimize._lbfgsb_py',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_lbfgsb_py.py',
   'PYMODULE'),
  ('scipy.optimize._minpack_py',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_minpack_py.py',
   'PYMODULE'),
  ('scipy.optimize._root_scalar',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_root_scalar.py',
   'PYMODULE'),
  ('scipy.optimize._zeros_py',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_zeros_py.py',
   'PYMODULE'),
  ('scipy.optimize._root',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_root.py',
   'PYMODULE'),
  ('scipy.optimize._spectral',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_spectral.py',
   'PYMODULE'),
  ('scipy.optimize._linesearch',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_linesearch.py',
   'PYMODULE'),
  ('scipy.optimize._dcsrch',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_dcsrch.py',
   'PYMODULE'),
  ('scipy.optimize._nonlin',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_nonlin.py',
   'PYMODULE'),
  ('scipy.optimize._minimize',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_minimize.py',
   'PYMODULE'),
  ('scipy.optimize._cobyqa_py',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_cobyqa_py.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\cobyqa\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\cobyqa\\utils\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils.versions',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\cobyqa\\utils\\versions.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils.math',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\cobyqa\\utils\\math.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils.exceptions',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\cobyqa\\utils\\exceptions.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.main',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\cobyqa\\main.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.settings',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\cobyqa\\settings.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.problem',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\cobyqa\\problem.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.framework',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\cobyqa\\framework.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.subsolvers.optim',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\cobyqa\\subsolvers\\optim.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.subsolvers',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\cobyqa\\subsolvers\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.subsolvers.geometry',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\cobyqa\\subsolvers\\geometry.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.models',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\cobyqa\\models.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.minimize_trustregion_constr',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\minimize_trustregion_constr.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.report',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\report.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.tr_interior_point',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\tr_interior_point.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.canonical_constraint',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\canonical_constraint.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.equality_constrained_sqp',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\equality_constrained_sqp.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.qp_subproblem',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\qp_subproblem.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.projections',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\projections.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_exact',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trustregion_exact.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trustregion.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_krylov',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trustregion_krylov.py',
   'PYMODULE'),
  ('scipy.optimize._trlib',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trlib\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_ncg',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trustregion_ncg.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_dogleg',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trustregion_dogleg.py',
   'PYMODULE'),
  ('scipy.optimize._optimize',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_optimize.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Python\\Python310\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('threading', 'C:\\Python\\Python310\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Python\\Python310\\lib\\_threading_local.py',
   'PYMODULE'),
  ('matplotlib.figure',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\figure.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_tkagg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\backends\\backend_tkagg.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'C:\\Python\\Python310\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Python\\Python310\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'C:\\Python\\Python310\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Python\\Python310\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.ttk', 'C:\\Python\\Python310\\lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('tkinter', 'C:\\Python\\Python310\\lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.constants',
   'C:\\Python\\Python310\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('scipy.linalg',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\__init__.py',
   'PYMODULE'),
  ('scipy.linalg.matfuncs',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\matfuncs.py',
   'PYMODULE'),
  ('scipy.linalg.special_matrices',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\special_matrices.py',
   'PYMODULE'),
  ('scipy.linalg.misc',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\misc.py',
   'PYMODULE'),
  ('scipy.linalg.basic',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\basic.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_schur',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\decomp_schur.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_svd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\decomp_svd.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_qr',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\decomp_qr.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_lu',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\decomp_lu.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_cholesky',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\decomp_cholesky.py',
   'PYMODULE'),
  ('scipy.linalg.decomp',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\decomp.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_cossin',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_decomp_cossin.py',
   'PYMODULE'),
  ('scipy.linalg._sketches',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_sketches.py',
   'PYMODULE'),
  ('scipy.linalg._procrustes',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_procrustes.py',
   'PYMODULE'),
  ('scipy.linalg._solvers',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_solvers.py',
   'PYMODULE'),
  ('scipy.linalg._special_matrices',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_special_matrices.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_matfuncs.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs_inv_ssq',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_matfuncs_inv_ssq.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs_sqrtm',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_matfuncs_sqrtm.py',
   'PYMODULE'),
  ('scipy.linalg._expm_frechet',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_expm_frechet.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_polar',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_decomp_polar.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_schur',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_decomp_schur.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_qz',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_decomp_qz.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_cholesky',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_decomp_cholesky.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_ldl',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_decomp_ldl.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_lu',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_decomp_lu.py',
   'PYMODULE'),
  ('numpy',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy.char',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy.rec',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('fileinput', 'C:\\Python\\Python310\\lib\\fileinput.py', 'PYMODULE'),
  ('numpy.f2py.symbolic',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.matlib',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE')],
 [('python310.dll', 'C:\\Python\\Python310\\python310.dll', 'BINARY'),
  ('scipy.libs\\libscipy_openblas-f07f5a5d207a3a47104dca54d6d0c86a.dll',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy.libs\\libscipy_openblas-f07f5a5d207a3a47104dca54d6d0c86a.dll',
   'BINARY'),
  ('llvmlite\\binding\\llvmlite.dll',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\llvmlite.dll',
   'BINARY'),
  ('select.pyd', 'C:\\Python\\Python310\\DLLs\\select.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\Python\\Python310\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\Python\\Python310\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Python\\Python310\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('pyexpat.pyd', 'C:\\Python\\Python310\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\Python\\Python310\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Python\\Python310\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd', 'C:\\Python\\Python310\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Python\\Python310\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_socket.pyd', 'C:\\Python\\Python310\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'C:\\Python\\Python310\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\Python\\Python310\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_msi.pyd', 'C:\\Python\\Python310\\DLLs\\_msi.pyd', 'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Python\\Python310\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd', 'C:\\Python\\Python310\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('markupsafe\\_speedups.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\markupsafe\\_speedups.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_quadpack.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_quadpack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_ccallback_c.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_ccallback_c.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ellip_harm_2.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_ellip_harm_2.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_gufuncs.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_gufuncs.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_special_ufuncs.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_special_ufuncs.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_comb.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_comb.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_specfun.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_specfun.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_ufuncs.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs_cxx.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_ufuncs_cxx.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\random\\_philox.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\random\\_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\random\\_common.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_group_columns.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_group_columns.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rgi_cython.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_rgi_cython.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_interpnd.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_interpnd.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_bspl.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_bspl.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\transform\\_rotation.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\transform\\_rotation.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_pybind.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\_distance_pybind.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_hausdorff.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\_hausdorff.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_wrap.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\_distance_wrap.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_c_internal_utils.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_c_internal_utils.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\_imagingtk.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\_webp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_avif.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\_avif.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\_imagingcms.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\_imagingmath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Python\\Python310\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\_imaging.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'C:\\Python\\Python310\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('matplotlib\\backends\\_tkagg.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\backends\\_tkagg.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\backends\\_backend_agg.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\backends\\_backend_agg.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\ft2font.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\ft2font.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('kiwisolver\\_cext.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\kiwisolver\\_cext.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_image.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_image.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('contourpy\\_contourpy.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\contourpy\\_contourpy.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_tri.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_tri.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_qhull.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_qhull.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_path.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_path.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_voronoi.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\_voronoi.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_qhull.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\_qhull.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_ckdtree.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\_ckdtree.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rbfinterp_pythran.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_rbfinterp_pythran.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_flapack.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_flapack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_fblas.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_fblas.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_ppoly.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_ppoly.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_dierckx.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_dierckx.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_dfitpack.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_dfitpack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_fitpack.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_fitpack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_lsoda.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_lsoda.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_dop.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_dop.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_vode.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_vode.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_rank_filter_1d.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\_rank_filter_1d.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_nd_image.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\_nd_image.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_ni_label.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\_ni_label.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_qmc_cy.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_qmc_cy.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_sobol.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_sobol.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_tools.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\csgraph\\_tools.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_reordering.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\csgraph\\_reordering.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_matching.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\csgraph\\_matching.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_flow.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\csgraph\\_flow.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_min_spanning_tree.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\csgraph\\_min_spanning_tree.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_traversal.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\csgraph\\_traversal.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_shortest_path.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\csgraph\\_shortest_path.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats_pythran.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_stats_pythran.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\fft\\_pocketfft\\pypocketfft.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\_pocketfft\\pypocketfft.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_uarray\\_uarray.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_uarray\\_uarray.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_rcont\\rcont.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_rcont\\rcont.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_mvn.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_mvn.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_ansari_swilk_statistics.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_ansari_swilk_statistics.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_levy_stable\\levyst.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_levy_stable\\levyst.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_biasedurn.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_biasedurn.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\_cffi_backend.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_stats.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_odepack.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_odepack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_blas.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\cython_blas.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_lapack.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\cython_lapack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\cython_special.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\cython_special.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_fpumode.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_fpumode.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\messagestream.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\messagestream.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_csparsetools.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_csparsetools.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_sparsetools.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_sparsetools.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_cythonized_array_utils.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_cythonized_array_utils.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_solve_toeplitz.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_solve_toeplitz.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_zpropack.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_zpropack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_cpropack.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_cpropack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_dpropack.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_dpropack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_spropack.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_spropack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_dsolve\\_superlu.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\_superlu.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\core\\typeconv\\_typeconv.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typeconv\\_typeconv.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\experimental\\jitclass\\_box.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\experimental\\jitclass\\_box.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\core\\runtime\\_nrt_python.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\runtime\\_nrt_python.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\np\\ufunc\\workqueue.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\ufunc\\workqueue.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\np\\ufunc\\omppool.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\ufunc\\omppool.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\np\\ufunc\\tbbpool.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\ufunc\\tbbpool.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\np\\ufunc\\_internal.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\ufunc\\_internal.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\cuda\\cudadrv\\_extras.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\_extras.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\_devicearray.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\_devicearray.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\mviewbuf.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\mviewbuf.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\_dispatcher.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\_dispatcher.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\_helperlib.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\_helperlib.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\_dynfunc.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\_dynfunc.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highspy\\_core.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_highspy\\_core.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highspy\\_highs_options.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_highspy\\_highs_options.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_direct.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_direct.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_pava_pybind.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_pava_pybind.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsq\\givens_elimination.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_lsq\\givens_elimination.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsap.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_lsap.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_interpolative.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_decomp_interpolative.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_bglu_dense.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_bglu_dense.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_cython_nnls.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_cython_nnls.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_slsqp.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_slsqp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_zeros.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_zeros.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_minpack.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_minpack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_trlib\\_trlib.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trlib\\_trlib.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_cobyla.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_cobyla.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_moduleTNC.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_moduleTNC.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lbfgsb.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_lbfgsb.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter.pyd', 'C:\\Python\\Python310\\DLLs\\_tkinter.pyd', 'EXTENSION'),
  ('scipy\\linalg\\_decomp_update.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_decomp_update.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_linalg_pythran.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_linalg_pythran.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_expm.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_matfuncs_expm.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_sqrtm_triu.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_matfuncs_sqrtm_triu.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_lu_cython.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_decomp_lu_cython.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\md.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp310-win_amd64.pyd',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll', 'C:\\Python\\Python310\\VCRUNTIME140.dll', 'BINARY'),
  ('MSVCP140.dll', 'C:\\Windows\\system32\\MSVCP140.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'C:\\Python\\Python310\\VCRUNTIME140_1.dll', 'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Python\\Python310\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll', 'C:\\Python\\Python310\\DLLs\\libssl-1_1.dll', 'BINARY'),
  ('libffi-7.dll', 'C:\\Python\\Python310\\DLLs\\libffi-7.dll', 'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('scipy\\special\\libsf_error_state.dll',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\libsf_error_state.dll',
   'BINARY'),
  ('VCOMP140.DLL', 'C:\\Windows\\system32\\VCOMP140.DLL', 'BINARY'),
  ('tk86t.dll', 'C:\\Python\\Python310\\DLLs\\tk86t.dll', 'BINARY'),
  ('tcl86t.dll', 'C:\\Python\\Python310\\DLLs\\tcl86t.dll', 'BINARY')],
 [],
 [],
 [('matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.pdf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back_large.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\back_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.pdf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave_large.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.pdf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward_large.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\forward_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.pdf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.pdf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help_large.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\help_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.pdf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home_large.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\home_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.pdf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move_large.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\move_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.pdf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots_large.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\kpsewhich.lua',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\kpsewhich.lua',
   'DATA'),
  ('matplotlib\\mpl-data\\matplotlibrc',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\matplotlibrc',
   'DATA'),
  ('matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\README.txt',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\README.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Stocks.csv',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Stocks.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\goog.npz',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\goog.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\logo2.png',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\logo2.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\msft.csv',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\msft.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\petroff10.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\petroff10.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-bright.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-bright.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-colorblind.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-colorblind.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark-palette.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark-palette.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-darkgrid.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-darkgrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-deep.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-deep.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-muted.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-muted.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-notebook.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-notebook.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-paper.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-paper.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-pastel.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-pastel.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-poster.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-poster.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-talk.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-talk.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-ticks.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-ticks.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-white.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-white.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-whitegrid.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-whitegrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'DATA'),
  ('base_library.zip',
   'G:\\wangchaoyu\\pwe-calculate\\build\\Photonic_Band_Calculator\\base_library.zip',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Python\\Python310\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Python\\Python310\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maseru',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('tcl\\tzdata\\Portugal',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('tcl\\tzdata\\America\\Los_Angeles',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('tcl\\encoding\\iso8859-14.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('tk\\panedwindow.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Uzhgorod',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('tcl\\tzdata\\America\\Guatemala',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimbu',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('tcl\\msgs\\ru.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('tk\\scale.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\scale.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Srednekolymsk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('tcl\\encoding\\iso8859-11.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('tcl\\tzdata\\US\\Eastern',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('tcl\\msgs\\en_nz.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\..\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ljubljana',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('tcl\\encoding\\macTurkish.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('tcl\\encoding\\macCentEuro.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bucharest',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('tcl\\msgs\\fa.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Puerto_Rico',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Davis',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('tcl\\tzdata\\America\\Pangnirtung',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('tcl\\msgs\\es_cl.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('tcl\\msgs\\bg.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('tcl\\encoding\\gb2312.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Isle_of_Man',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('tcl\\tzdata\\US\\Samoa',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dakar',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('tcl\\encoding\\macThai.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sofia',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Palmer',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('tcl\\tzdata\\America\\Chicago',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('tcl\\msgs\\en_gb.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mbabane',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('tcl\\msgs\\hi_in.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Majuro',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('tk\\images\\pwrdLogo.eps',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('tcl\\tzdata\\America\\Mendoza',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Urumqi',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('tcl\\tzdata\\America\\Buenos_Aires',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\US\\Mountain',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('tcl\\tzdata\\America\\Rankin_Inlet',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('tcl\\parray.tcl', 'C:\\Python\\Python310\\tcl\\tcl8.6\\parray.tcl', 'DATA'),
  ('tcl\\msgs\\te.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Cocos',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('tcl\\encoding\\ebcdic.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-7',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Atyrau',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('tcl\\tzdata\\America\\Scoresbysund',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jayapura',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Stanley',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Rothera',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baghdad',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Ponape',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Center',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pohnpei',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('tcl\\msgs\\ar.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('tk\\ttk\\classicTheme.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('tcl\\encoding\\cp850.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mahe',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yerevan',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('tcl\\tzdata\\Navajo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dacca',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('tcl\\encoding\\big5.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Nauru',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('tcl\\msgs\\zh_hk.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Dublin',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vaduz',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('tk\\listbox.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\listbox.tcl', 'DATA'),
  ('tk\\msgs\\fr.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayman',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tehran',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bamako',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('tcl\\encoding\\macUkraine.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-16.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Punta_Arenas',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('tcl\\tzdata\\Arctic\\Longyearbyen',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-3',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('tcl\\tzdata\\America\\Chihuahua',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nairobi',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('tcl\\msgs\\ko.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('tcl\\encoding\\jis0208.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('tcl\\msgs\\sw.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tarawa',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('tcl\\tzdata\\America\\Inuvik',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('tcl\\encoding\\koi8-r.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('tk\\ttk\\utils.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Melbourne',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('tcl\\tzdata\\Universal',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('tk\\msgs\\de.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('tk\\ttk\\clamTheme.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hovd',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('tcl\\tzdata\\America\\Monterrey',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('tcl\\encoding\\cp852.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('tcl\\tm.tcl', 'C:\\Python\\Python310\\tcl\\tcl8.6\\tm.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Colombo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Winamac',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('tcl\\tzdata\\America\\Paramaribo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Port_Moresby',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('tcl\\msgs\\en_zw.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('tcl\\msgs\\es_do.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('tcl\\http1.0\\pkgIndex.tcl',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\msgs\\fr.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('tcl\\encoding\\macDingbats.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Anchorage',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('tcl\\tzdata\\US\\Michigan',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-11',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Bougainville',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Cordoba',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('tcl\\http1.0\\http.tcl',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Sydney',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\DumontDUrville',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('tcl\\encoding\\macRoman.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Paris',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('tk\\scrlbar.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\scrlbar.tcl', 'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-8',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\La_Rioja',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Buenos_Aires',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Andorra',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('tcl\\msgs\\tr.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('tk\\unsupported.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashkhabad',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('tcl\\msgs\\ms_my.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Grand_Turk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('tk\\palette.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\palette.tcl', 'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-14',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Gaza',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vincennes',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Luanda',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('tk\\ttk\\panedwindow.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('tk\\ttk\\notebook.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('tcl\\msgs\\es_ar.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Katmandu',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('tcl\\tzdata\\America\\Yakutat',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('tcl\\msgs\\bn_in.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Yancowinna',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('tcl\\tzdata\\America\\Santarem',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8PDT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Canberra',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kabul',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Addis_Ababa',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kamchatka',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('tcl\\msgs\\et.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dushanbe',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('tcl\\encoding\\iso8859-2.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\West',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Barnaul',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\St_Helena',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('tcl\\encoding\\shiftjis.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('tcl\\msgs\\en_hk.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sarajevo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('tcl\\tzdata\\EET',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bratislava',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Phnom_Penh',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Mountain',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ndjamena',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('tcl\\tzdata\\Egypt',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Reykjavik',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Antananarivo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Sao_Tome',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('tk\\images\\pwrdLogo150.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('tk\\bgerror.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\bgerror.tcl', 'DATA'),
  ('tk\\choosedir.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('tcl\\tzdata\\WET',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Pacific',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Saskatchewan',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('tcl\\auto.tcl', 'C:\\Python\\Python310\\tcl\\tcl8.6\\auto.tcl', 'DATA'),
  ('tcl\\tzdata\\Europe\\Samara',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-13',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dili',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulan_Bator',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Tasmania',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('tcl\\msgs\\ga_ie.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('tcl\\msgs\\kl_gl.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('tcl\\msgs\\gl_es.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\West',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Minsk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Matamoros',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('tcl\\tzdata\\America\\Metlakatla',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('tcl\\encoding\\cp737.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Marigot',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Muscat',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Maldives',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kanton',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belgrade',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('tcl\\tzdata\\America\\El_Salvador',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('tcl\\msgs\\hu.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('tcl\\msgs\\te_in.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashgabat',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('tcl\\msgs\\nn.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Barthelemy',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('tcl\\msgs\\fi.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-1.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novokuznetsk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('tcl\\encoding\\macRomania.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Ushuaia',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+5',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('tcl\\tzdata\\Poland',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Warsaw',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('tcl\\tzdata\\America\\Santa_Isabel',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('tk\\msgbox.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\msgbox.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Rangoon',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\ComodRivadavia',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Macquarie',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('tcl\\tzdata\\America\\Kralendijk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fiji',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Troll',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('tk\\comdlg.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\comdlg.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Manaus',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Casey',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('tcl\\clock.tcl', 'C:\\Python\\Python310\\tcl\\tcl8.6\\clock.tcl', 'DATA'),
  ('tcl\\tzdata\\Europe\\Monaco',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('tcl\\tzdata\\GB', 'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\GB', 'DATA'),
  ('tcl\\tzdata\\Chile\\EasterIsland',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('tcl\\msgs\\zh_tw.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Gaborone',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Harbin',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('tcl\\encoding\\cp1254.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Karachi',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('tcl\\tzdata\\America\\Regina',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mayotte',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmera',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.18.tm',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\..\\tcl8\\8.4\\platform-1.0.18.tm',
   'DATA'),
  ('tcl\\tzdata\\America\\Goose_Bay',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Niue',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('tk\\xmfbox.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\xmfbox.tcl', 'DATA'),
  ('tcl\\tzdata\\Europe\\Berlin',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('tcl\\tzdata\\ROC',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('tk\\safetk.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\safetk.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Choibalsan',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('tcl\\tzdata\\Japan',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\McMurdo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('tcl\\encoding\\cp1256.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('tcl\\encoding\\gb1988.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('tk\\ttk\\defaults.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('tcl\\encoding\\cp775.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tbilisi',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('tcl\\tzdata\\America\\Boise',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('tcl\\tzdata\\GMT-0',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Luis',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('tcl\\msgs\\it.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Douala',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('tk\\icons.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\icons.tcl', 'DATA'),
  ('tcl\\encoding\\cp1250.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Port_of_Spain',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('tcl\\encoding\\cp861.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+6',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tripoli',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Wayne',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('tcl\\encoding\\cp862.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('tk\\msgs\\pl.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Lucia',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('tcl\\tzdata\\EST5EDT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\America\\Guyana',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('tcl\\tzdata\\America\\Yellowknife',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('tk\\tk.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('tk\\ttk\\winTheme.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\ROK',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('tcl\\msgs\\mr_in.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kaliningrad',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Darwin',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Adelaide',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayenne',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Krasnoyarsk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pitcairn',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Amman',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Louisville',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tcl\\tzdata\\UCT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('tcl\\msgs\\en_za.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-5.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('tcl\\msgs\\pt.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('tcl\\tzdata\\Eire',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Midway',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('tcl\\tzdata\\GB-Eire',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Jujuy',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\America\\Merida',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('tcl\\encoding\\euc-jp.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('tk\\images\\logo.eps',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hebron',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('tcl\\tzdata\\America\\Atka',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Brazzaville',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('tcl\\msgs\\sh.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('tcl\\tzdata\\Hongkong',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('tcl\\msgs\\sr.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('tk\\megawidget.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Reunion',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('tcl\\safe.tcl', 'C:\\Python\\Python310\\tcl\\tcl8.6\\safe.tcl', 'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+12',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('tcl\\msgs\\zh_cn.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('tcl\\msgs\\ja.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pago_Pago',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('tk\\msgs\\cs.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Christmas',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('tk\\msgs\\en.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tallinn',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('tcl\\encoding\\iso2022.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\San_Marino',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('tcl\\msgs\\fr_ca.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('tk\\images\\logo100.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('tcl\\msgs\\ta_in.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Cambridge_Bay',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('tcl\\tzdata\\US\\East-Indiana',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+2',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('tcl\\msgs\\hr.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kiev',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Tell_City',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Niamey',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('tcl\\tzdata\\Canada\\East-Saskatchewan',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('tcl\\tzdata\\America\\Winnipeg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('tcl\\tzdata\\America\\Santiago',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('tcl\\tzdata\\America\\Moncton',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('tcl\\tzdata\\America\\Port-au-Prince',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('tcl\\tzdata\\US\\Alaska',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Greenwich',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Riyadh',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vientiane',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('tcl\\tzdata\\America\\Rainy_River',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jerusalem',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('tcl\\tzdata\\Africa\\El_Aaiun',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lord_Howe',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Porto-Novo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Efate',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('tcl\\tzdata\\NZ', 'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\NZ', 'DATA'),
  ('tcl\\tzdata\\America\\Montreal',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Acre',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kashgar',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Broken_Hill',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tashkent',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('tcl\\msgs\\kok.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('tcl\\encoding\\cp1252.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vladivostok',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('tcl\\msgs\\nl_be.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('tcl\\msgs\\ar_jo.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Oslo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('tcl\\tzdata\\Australia\\ACT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('tk\\clrpick.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\clrpick.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Singapore',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('tcl\\tzdata\\America\\Danmarkshavn',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qostanay',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-2',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('tcl\\tzdata\\America\\Havana',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuwait',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kiritimati',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Istanbul',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wallis',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('tcl\\tzdata\\America\\Curacao',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('tcl\\msgs\\ro.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Saigon',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('tcl\\tzdata\\America\\Boa_Vista',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('tcl\\encoding\\macCyrillic.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('tcl\\msgs\\de_at.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Budapest',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Guernsey',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('tcl\\msgs\\en_bw.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('tcl\\msgs\\es_ec.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Banjul',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('tcl\\msgs\\es_sv.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('tk\\ttk\\altTheme.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('tk\\focus.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\focus.tcl', 'DATA'),
  ('tk\\ttk\\xpTheme.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Skopje',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Shanghai',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('tcl\\tzdata\\CET',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tiraspol',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+10',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Comoro',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('tcl\\tzdata\\America\\Santo_Domingo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Samarkand',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('tcl\\msgs\\fo.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('tcl\\encoding\\macGreek.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('tcl\\msgs\\es_pa.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Lisbon',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('tk\\images\\logo64.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('tcl\\msgs\\ar_sy.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lusaka',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('tcl\\tzdata\\America\\Lower_Princes',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Gambier',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UTC',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Nelson',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Auckland',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Manila',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('tcl\\tzdata\\America\\Tijuana',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('tcl\\msgs\\fa_ir.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('tcl\\encoding\\jis0212.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('tk\\console.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\console.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Thule',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('tcl\\msgs\\kl.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guadalcanal',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Algiers',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Monrovia',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('tcl\\tzdata\\America\\Whitehorse',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5EDT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Rome',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('tcl\\tzdata\\America\\Rosario',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kathmandu',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ust-Nera',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('tcl\\tzdata\\Europe\\London',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Beirut',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dar_es_Salaam',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Hobart',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bissau',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lubumbashi',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Central',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Easter',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('tcl\\tzdata\\America\\Shiprock',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Enderbury',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Timbuktu',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('tcl\\tzdata\\America\\Anguilla',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('tcl\\tzdata\\PRC',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('tk\\dialog.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\dialog.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Knox',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('tcl\\encoding\\euc-kr.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('tk\\msgs\\da.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('tcl\\msgs\\en_be.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('tcl\\msgs\\es_uy.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('tk\\images\\pwrdLogo75.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.5.tm',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\..\\tcl8\\8.6\\http-2.9.5.tm',
   'DATA'),
  ('tk\\pkgIndex.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maputo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('tcl\\msgs\\en_in.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Stockholm',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('tcl\\tzdata\\America\\Denver',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('tk\\ttk\\vistaTheme.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('tcl\\encoding\\iso2022-jp.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Antigua',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Chagos',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Eucla',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('tcl\\msgs\\ms.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Tortola',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('tcl\\encoding\\iso8859-7.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Menominee',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('tcl\\msgs\\de.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-10',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Prague',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('tcl\\msgs\\gl.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('tk\\ttk\\fonts.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6CDT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Casablanca',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-9',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chatham',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('tcl\\tzdata\\America\\Nuuk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('tk\\ttk\\button.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Brunei',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('tcl\\tzdata\\MST',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Conakry',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('tk\\msgs\\es.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('tcl\\msgs\\da.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('tcl\\msgs\\zh_sg.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('tcl\\encoding\\cp863.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Ensenada',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('tcl\\encoding\\macIceland.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kirov',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ouagadougou',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('tcl\\tzdata\\America\\Guadeloupe',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Marquesas',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vevay',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('tk\\entry.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\entry.tcl', 'DATA'),
  ('tcl\\tzdata\\Turkey',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('tcl\\tzdata\\MST7MDT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-0',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('tcl\\encoding\\gb12345.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Nicosia',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\America\\Cordoba',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Famagusta',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Canary',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('tcl\\msgs\\pl.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tokyo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('tcl\\tzdata\\PST8PDT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('tcl\\tzdata\\America\\Caracas',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Petersburg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('tcl\\encoding\\iso2022-kr.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Eirunepe',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('tcl\\msgs\\es_py.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kigali',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('tcl\\opt0.4\\pkgIndex.tcl',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('tk\\images\\logoLarge.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Simferopol',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Yukon',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tel_Aviv',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('tcl\\msgs\\fr_be.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lome',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('tcl\\tzdata\\W-SU',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Magadan',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimphu',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('tcl\\tzdata\\Australia\\NSW',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+11',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Johns',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('tcl\\tzdata\\America\\Nassau',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Thomas',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Juba',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Calcutta',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('tcl\\msgs\\cs.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('tcl\\msgs\\mr.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Jan_Mayen',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tcl\\msgs\\nl.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('tcl\\msgs\\ru_ua.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pyongyang',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('tcl\\encoding\\cp437.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Rarotonga',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tahiti',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9YDT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-5',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Mawson',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7MDT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('tcl\\tzdata\\US\\Aleutian',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('tcl\\msgs\\mt.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Jersey',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hong_Kong',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('tcl\\msgs\\en_ca.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT0',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('tcl\\tzdata\\America\\Atikokan',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('tk\\ttk\\sizegrip.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Mazatlan',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bahrain',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Malabo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Windhoek',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('tk\\menu.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\menu.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Mexico_City',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Copenhagen',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Monticello',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zaporozhye',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('tcl\\tzdata\\America\\Miquelon',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Madrid',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Zulu',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('tcl\\msgs\\fa_in.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('tcl\\msgs\\es_cr.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('tk\\ttk\\cursors.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('tcl\\msgs\\es.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Knox_IN',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('tk\\optMenu.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\optMenu.tcl', 'DATA'),
  ('tcl\\msgs\\lv.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Astrakhan',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('tcl\\msgs\\pt_br.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('tcl\\tzdata\\US\\Indiana-Starke',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('tcl\\msgs\\eu_es.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('tk\\ttk\\aquaTheme.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wake',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaNorte',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('tcl\\msgs\\it_ch.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Funafuti',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vienna',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bangui',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baku',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('tcl\\msgs\\hi.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Sakhalin',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('tcl\\tzdata\\America\\Maceio',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macau',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yangon',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('tcl\\tzdata\\America\\Catamarca',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\America\\Nome',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('tcl\\tzdata\\GMT+0',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('tcl\\word.tcl', 'C:\\Python\\Python310\\tcl\\tcl8.6\\word.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Tomsk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\General',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bangkok',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('tcl\\msgs\\en_ie.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kosrae',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('tcl\\tzdata\\Cuba',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('tk\\images\\pwrdLogo100.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('tcl\\init.tcl', 'C:\\Python\\Python310\\tcl\\tcl8.6\\init.tcl', 'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+7',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('tcl\\msgs\\th.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dubai',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson_Creek',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chuuk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vatican',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+3',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('tcl\\encoding\\tis-620.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ujung_Pandang',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Khartoum',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmara',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('tcl\\tzdata\\America\\Edmonton',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UCT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('tcl\\encoding\\cp860.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-9.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Ojinaga',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Chisinau',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Universal',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('tcl\\encoding\\gb2312-raw.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Vincent',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('tcl\\tzdata\\Jamaica',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yakutsk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Halifax',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chita',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('tcl\\tzdata\\NZ-CHAT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('tcl\\encoding\\iso8859-10.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('tcl\\encoding\\cp866.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Recife',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('tcl\\encoding\\cp1255.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('tk\\text.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\text.tcl', 'DATA'),
  ('tcl\\encoding\\cp1258.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('tcl\\tzdata\\America\\La_Paz',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vilnius',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chongqing',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('tcl\\encoding\\iso8859-3.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('tcl\\tzdata\\Iceland',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faroe',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('tcl\\tzdata\\America\\Sitka',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('tk\\spinbox.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\spinbox.tcl', 'DATA'),
  ('tcl\\tzdata\\Pacific\\Honolulu',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Nicosia',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('tcl\\msgs\\kw.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('tk\\license.terms',
   'C:\\Python\\Python310\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('tk\\ttk\\scrollbar.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('tcl\\msgs\\bn.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Asuncion',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lindeman',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zurich',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Johannesburg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('tcl\\tzdata\\America\\Dominica',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Salta',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('tcl\\encoding\\symbol.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Kerguelen',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Malta',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('tcl\\tzdata\\America\\Phoenix',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('tcl\\encoding\\cp865.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\South_Pole',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('tcl\\tzdata\\Greenwich',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('tcl\\encoding\\iso8859-8.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jakarta',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tirane',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Seoul',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('tcl\\tzdata\\America\\Noronha',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Azores',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Libreville',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('tcl\\tzdata\\America\\Guayaquil',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('tcl\\msgs\\lt.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('tcl\\encoding\\cp874.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('tcl\\encoding\\cp936.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Podgorica',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('tcl\\tzdata\\America\\Fortaleza',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('tcl\\tzdata\\Australia\\North',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('tcl\\tzdata\\America\\Swift_Current',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Mendoza',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('tcl\\tzdata\\America\\Creston',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('tcl\\tzdata\\America\\Grenada',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('tcl\\tzdata\\America\\Lima',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('tcl\\msgs\\de_be.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('tcl\\msgs\\ko_kr.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('tk\\ttk\\combobox.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('tk\\msgs\\pt.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('tcl\\msgs\\ca.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('tcl\\msgs\\es_pr.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('tcl\\tzdata\\US\\Hawaii',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Almaty',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Queensland',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('tcl\\tzdata\\America\\Tegucigalpa',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('tcl\\encoding\\cp855.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('tcl\\msgs\\af_za.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('tcl\\msgs\\fr_ch.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kolkata',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulaanbaatar',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dhaka',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Juan',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tcl\\tzdata\\America\\Bogota',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Khandyga',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\East',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Syowa',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('tcl\\tzdata\\America\\Belize',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kampala',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('tcl\\msgs\\id.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-1',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Amsterdam',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Kitts',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Freetown',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('tk\\button.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\button.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Irkutsk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('tcl\\tzdata\\EST',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qyzylorda',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('tcl\\msgs\\ta.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Nipigon',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('tcl\\msgs\\eu.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Palau',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('tcl\\msgs\\af.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Costa_Rica',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtau',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chungking',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+0',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('tcl\\tzdata\\America\\Detroit',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\Acre',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Gibraltar',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('tcl\\tclIndex', 'C:\\Python\\Python310\\tcl\\tcl8.6\\tclIndex', 'DATA'),
  ('tcl\\tzdata\\Atlantic\\Bermuda',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pontianak',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('tcl\\tzdata\\America\\Glace_Bay',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kinshasa',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+8',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('tcl\\tzdata\\America\\Managua',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('tcl\\msgs\\es_bo.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific-New',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nouakchott',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('tcl\\tzdata\\US\\Arizona',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Madeira',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yekaterinburg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtobe',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('tcl\\encoding\\cp949.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('tcl\\encoding\\cp1257.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('tk\\msgs\\nl.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('tk\\images\\logoMed.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('tk\\ttk\\scale.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Godthab',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Atlantic',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('tk\\obsolete.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('tk\\msgs\\it.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('tcl\\msgs\\zh.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('tk\\ttk\\spinbox.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('tcl\\encoding\\ksc5601.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('tcl\\msgs\\es_mx.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('tcl\\msgs\\en_ph.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Sao_Paulo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Cairo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('tcl\\msgs\\fo_fo.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-6.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('tk\\tearoff.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\tearoff.tcl', 'DATA'),
  ('tcl\\msgs\\vi.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\..\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('tk\\ttk\\entry.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('tcl\\tzdata\\GMT0',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Perth',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('tcl\\tzdata\\HST',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\HST10',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Harare',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('tcl\\msgs\\sk.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('tcl\\encoding\\dingbats.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Norfolk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuala_Lumpur',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuching',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Blantyre',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('tcl\\msgs\\sl.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('tk\\mkpsenc.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\mkpsenc.tcl', 'DATA'),
  ('tcl\\tzdata\\Asia\\Oral',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('tk\\msgs\\ru.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Indianapolis',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tcl\\tzdata\\Australia\\LHI',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('tk\\fontchooser.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+9',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Riga',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Taipei',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('tcl\\tzdata\\America\\New_York',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Accra',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('tcl\\msgs\\gv.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Cancun',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('tcl\\msgs\\id_id.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.3.tm',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\..\\tcl8\\8.5\\tcltest-2.5.3.tm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macao',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('tcl\\msgs\\es_co.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('tk\\images\\tai-ku.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aden',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('tcl\\tzdata\\America\\Louisville',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('tcl\\encoding\\iso8859-15.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('tcl\\encoding\\cp950.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bujumbura',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kwajalein',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('tcl\\msgs\\en_sg.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('tcl\\msgs\\sv.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Tucuman',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Istanbul',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('tcl\\encoding\\euc-cn.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('tcl\\tzdata\\Kwajalein',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\Australia\\South',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Omsk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+1',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Yap',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('tcl\\msgs\\ga.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Moscow',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('tcl\\tzdata\\Iran',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('tk\\msgs\\sv.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('tcl\\msgs\\kw_gb.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Jamaica',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mauritius',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Helsinki',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ulyanovsk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('tk\\tkfbox.tcl', 'C:\\Python\\Python310\\tcl\\tk8.6\\tkfbox.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Thunder_Bay',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+4',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('tcl\\tzdata\\America\\Belem',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('tcl\\tzdata\\America\\Iqaluit',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('tcl\\msgs\\sq.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia_Banderas',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ho_Chi_Minh',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('tk\\images\\README',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('tcl\\encoding\\cp932.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4ADT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('tcl\\tzdata\\America\\Jujuy',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\America\\Aruba',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Apia',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Saipan',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('tcl\\msgs\\kok_in.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('tcl\\msgs\\eo.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('tcl\\msgs\\ar_in.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('tcl\\msgs\\el.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('tcl\\encoding\\cp869.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('tcl\\tzdata\\GMT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Truk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('tcl\\tzdata\\America\\Hermosillo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('tcl\\msgs\\nb.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('tcl\\msgs\\en_au.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Anadyr',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Samoa',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Brussels',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ceuta',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('tcl\\tzdata\\America\\Resolute',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guam',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaSur',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-4',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('tcl\\tzdata\\America\\Indianapolis',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('tcl\\msgs\\es_hn.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('tk\\ttk\\progress.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\South_Georgia',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('tcl\\tzdata\\America\\Cuiaba',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Djibouti',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\DeNoronha',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Makassar',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('tcl\\tzdata\\America\\Virgin',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('tcl\\tzdata\\America\\Araguaina',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('tcl\\encoding\\iso8859-4.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Busingen',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('tk\\tclIndex', 'C:\\Python\\Python310\\tcl\\tk8.6\\tclIndex', 'DATA'),
  ('tcl\\tzdata\\Europe\\Zagreb',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('tcl\\tzdata\\America\\Barbados',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('tcl\\tzdata\\Israel',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('tcl\\tzdata\\America\\Blanc-Sablon',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('tcl\\history.tcl',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Marengo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('tcl\\encoding\\cp864.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('tcl\\encoding\\jis0201.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('tcl\\package.tcl',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('tcl\\encoding\\koi8-u.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tongatapu',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\New_Salem',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('tcl\\tzdata\\Libya',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Luxembourg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-6',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Catamarca',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('tcl\\msgs\\is.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Damascus',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faeroe',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('tcl\\msgs\\es_pe.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('tcl\\msgs\\es_ve.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Montevideo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('tcl\\encoding\\cp857.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Noumea',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('tcl\\tzdata\\America\\Panama',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Victoria',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Newfoundland',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('tcl\\tzdata\\America\\Coral_Harbour',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('tcl\\encoding\\cp1253.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belfast',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Volgograd',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('tcl\\encoding\\macCroatian.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('tcl\\msgs\\he.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Juneau',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('tcl\\encoding\\iso8859-13.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('tcl\\msgs\\es_ni.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('tcl\\msgs\\mk.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('tcl\\tzdata\\MET',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('tk\\iconlist.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('tk\\ttk\\treeview.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('tcl\\tzdata\\Zulu',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('tk\\msgs\\el.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Montserrat',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('tcl\\tzdata\\America\\Vancouver',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('tcl\\tzdata\\UTC',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('tk\\ttk\\menubutton.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('tcl\\tzdata\\CST6CDT',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Cape_Verde',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Galapagos',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('tcl\\tzdata\\America\\Adak',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('tcl\\tzdata\\America\\Martinique',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Mariehamn',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Abidjan',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novosibirsk',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Saratov',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('tcl\\msgs\\gv_gb.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Beulah',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('tcl\\tzdata\\Singapore',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Vostok',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mogadishu',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Johnston',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Velho',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('tcl\\msgs\\be.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('tcl\\encoding\\macJapan.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('tk\\images\\pwrdLogo175.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('tcl\\encoding\\cns11643.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Eastern',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\Chile\\Continental',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('tk\\msgs\\en_gb.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tk\\msgs\\eo.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bishkek',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Athens',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('tcl\\msgs\\ar_lb.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('tk\\images\\pwrdLogo200.gif',
   'C:\\Python\\Python310\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('tcl\\encoding\\cp1251.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Brisbane',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('tk\\ttk\\ttk.tcl',
   'C:\\Python\\Python310\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('tcl\\opt0.4\\optparse.tcl',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Rio_Branco',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Currie',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('tcl\\msgs\\uk.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('tcl\\msgs\\es_gt.msg',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('tcl\\tzdata\\US\\Central',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('tcl\\tzdata\\America\\Campo_Grande',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tunis',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('tcl\\tzdata\\America\\Toronto',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('tcl\\encoding\\ascii.enc',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('tk\\msgs\\hu.msg',
   'C:\\Python\\Python310\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fakaofo',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-12',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qatar',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lagos',
   'C:\\Python\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('setuptools-63.2.0.dist-info\\RECORD',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools-63.2.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools-63.2.0.dist-info\\top_level.txt',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools-63.2.0.dist-info\\top_level.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'C:\\Python\\Python310\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'C:\\Python\\Python310\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.6.dist-info\\METADATA',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy-2.2.6.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.6.dist-info\\LICENSE.txt',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy-2.2.6.dist-info\\LICENSE.txt',
   'DATA'),
  ('setuptools-63.2.0.dist-info\\LICENSE',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools-63.2.0.dist-info\\LICENSE',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'C:\\Python\\Python310\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('setuptools-63.2.0.dist-info\\INSTALLER',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools-63.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'C:\\Python\\Python310\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\INSTALLER',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy-2.2.6.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools-63.2.0.dist-info\\REQUESTED',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools-63.2.0.dist-info\\REQUESTED',
   'DATA'),
  ('numpy-2.2.6.dist-info\\entry_points.txt',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy-2.2.6.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\REQUESTED',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy-2.2.6.dist-info\\REQUESTED',
   'DATA'),
  ('numpy-2.2.6.dist-info\\RECORD',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy-2.2.6.dist-info\\RECORD',
   'DATA'),
  ('setuptools-63.2.0.dist-info\\WHEEL',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools-63.2.0.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'C:\\Python\\Python310\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'C:\\Python\\Python310\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.6.dist-info\\WHEEL',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy-2.2.6.dist-info\\WHEEL',
   'DATA'),
  ('setuptools-63.2.0.dist-info\\METADATA',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools-63.2.0.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.6.dist-info\\DELVEWHEEL',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy-2.2.6.dist-info\\DELVEWHEEL',
   'DATA'),
  ('setuptools-63.2.0.dist-info\\entry_points.txt',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools-63.2.0.dist-info\\entry_points.txt',
   'DATA')])
