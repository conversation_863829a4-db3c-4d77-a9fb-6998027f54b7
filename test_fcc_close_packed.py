#!/usr/bin/env python3
"""
测试脚本：验证FCC密堆积参数和新的对称性点路径
"""

import numpy as np
import matplotlib.pyplot as plt
from photonic_band_calculator import (
    generate_fcc_reciprocal_lattice, 
    build_matrix, 
    solve_eigenvalue_problem
)

def test_fcc_close_packed_parameters():
    """测试FCC密堆积参数"""
    print("=== FCC密堆积参数测试 ===")
    
    # FCC密堆积理论计算
    fcc_radius = 1.0 / (2.0 * np.sqrt(2.0))
    fcc_volume_fraction = np.pi / (3.0 * np.sqrt(2.0))
    
    print(f"FCC密堆积理论球半径: {fcc_radius:.6f}")
    print(f"FCC密堆积体积分数: {fcc_volume_fraction:.6f} ({fcc_volume_fraction*100:.2f}%)")
    
    # 验证密堆积条件
    # 在FCC结构中，最近邻球心距离为a/√2，球直径为2*ra
    # 密堆积条件：2*ra = a/√2，即ra = a/(2√2)
    lattice_constant = 1.0  # 假设晶格常数为1
    nearest_neighbor_distance = lattice_constant / np.sqrt(2.0)
    sphere_diameter = 2 * fcc_radius
    
    print(f"\n密堆积验证:")
    print(f"晶格常数 a: {lattice_constant}")
    print(f"最近邻距离 a/√2: {nearest_neighbor_distance:.6f}")
    print(f"球直径 2*ra: {sphere_diameter:.6f}")
    print(f"密堆积条件满足: {abs(sphere_diameter - nearest_neighbor_distance) < 1e-10}")
    
    return fcc_radius, fcc_volume_fraction

def test_new_symmetry_path():
    """测试新的对称性点路径: X-U-L-η-X-W-K"""
    print("\n=== 新对称性点路径测试 ===")
    
    # 定义高对称点
    a = 2*np.pi
    high_sym_points = {
        "Γ": np.array([0.0, 0.0, 0.0]),
        "X": a*np.array([1.0, 0.0, 0.0]),
        "L": a*np.array([0.5, 0.5, 0.5]),
        "W": a*np.array([1.0, 0.5, 0.0]),
        "K": a*np.array([0.75, 0.75, 0.0]),
        "U": a*np.array([1.0, 0.25, 0.25]),
        "η": a*np.array([1.0, 1.0, 0.0])  # eta点
    }
    
    # 新的路径
    path_labels = ["X", "U", "L", "η", "X", "W", "K"]
    
    print("高对称点坐标:")
    for label, point in high_sym_points.items():
        print(f"{label:2s}: ({point[0]:.3f}, {point[1]:.3f}, {point[2]:.3f})")
    
    print(f"\n新路径: {' → '.join(path_labels)}")
    
    # 计算路径长度
    total_length = 0
    for i in range(len(path_labels) - 1):
        start_point = high_sym_points[path_labels[i]]
        end_point = high_sym_points[path_labels[i+1]]
        segment_length = np.linalg.norm(end_point - start_point)
        total_length += segment_length
        print(f"{path_labels[i]} → {path_labels[i+1]}: 长度 = {segment_length:.3f}")
    
    print(f"总路径长度: {total_length:.3f}")
    
    return high_sym_points, path_labels

def calculate_band_structure_new_path():
    """使用新参数和路径计算带结构"""
    print("\n=== 使用新参数计算带结构 ===")
    
    # FCC密堆积参数
    ra = 1.0 / (2.0 * np.sqrt(2.0))  # FCC密堆积半径
    epsilon_a = 11.56  # 硅
    epsilon_b = 1.0    # 空气
    f = 4*np.pi*(ra**3)/3
    n_max = 3
    num_bands = 8
    
    print(f"参数设置:")
    print(f"  球半径 ra: {ra:.6f}")
    print(f"  介电常数 εa: {epsilon_a}")
    print(f"  介电常数 εb: {epsilon_b}")
    print(f"  体积分数 f: {f:.6f}")
    
    # 生成倒格矢量
    G_vectors = generate_fcc_reciprocal_lattice(n_max, use_cutoff=True)
    print(f"  使用平面波数量: {len(G_vectors)}")
    
    # 定义高对称点和路径
    a = 2*np.pi
    high_sym_points = {
        "X": a*np.array([1.0, 0.0, 0.0]),
        "U": a*np.array([1.0, 0.25, 0.25]),
        "L": a*np.array([0.5, 0.5, 0.5]),
        "η": a*np.array([1.0, 1.0, 0.0]),
        "W": a*np.array([1.0, 0.5, 0.0]),
        "K": a*np.array([0.75, 0.75, 0.0])
    }
    
    path_labels = ["X", "U", "L", "η", "X", "W", "K"]
    points_per_segment = 15
    
    # 构建k路径
    k_path = []
    k_label_positions = []
    k_labels = []
    current_pos = 0
    
    for i in range(len(path_labels) - 1):
        start_point = high_sym_points[path_labels[i]]
        end_point = high_sym_points[path_labels[i+1]]
        
        for j in range(points_per_segment):
            if j == 0 and i == 0:
                # 第一个点
                k_path.append(start_point)
                k_labels.append(path_labels[i])
                k_label_positions.append(current_pos)
                current_pos += 1
            elif j == 0:
                # 跳过重复的起点
                continue
            elif j == points_per_segment - 1:
                # 终点
                k_path.append(end_point)
                k_labels.append(path_labels[i+1])
                k_label_positions.append(current_pos)
                current_pos += 1
            else:
                # 中间点
                t = j / (points_per_segment - 1)
                k = (1-t)*start_point + t*end_point
                k_path.append(k)
                current_pos += 1
    
    k_path = np.array(k_path)
    print(f"  k路径点数: {len(k_path)}")
    
    # 计算带结构
    bands = []
    print("\n计算带结构...")
    
    for i, k in enumerate(k_path):
        if i % 10 == 0:
            print(f"  进度: {i+1}/{len(k_path)}")
        
        # 构建矩阵
        H = build_matrix(k, G_vectors, ra, epsilon_a, epsilon_b, f)
        
        # 求解特征值
        eigenvalues = solve_eigenvalue_problem(H, num_bands, "eigh")
        frequencies = np.sqrt(eigenvalues)
        bands.append(frequencies)
    
    bands = np.array(bands)
    
    # 分析结果
    print("\n=== 带结构分析 ===")
    
    # 显示高对称点频率
    print("高对称点频率:")
    for i, pos in enumerate(k_label_positions):
        if pos < len(bands):
            label = k_labels[i]
            freqs = bands[pos, :5]  # 显示前5个频率
            print(f"  {label}点: {freqs}")
    
    # 寻找带隙
    print("\n带隙分析:")
    complete_gaps = []
    
    for i in range(bands.shape[1] - 1):
        lower_band = bands[:, i]
        upper_band = bands[:, i+1]
        
        lower_max = np.max(lower_band)
        upper_min = np.min(upper_band)
        
        if upper_min > lower_max:
            gap_size = upper_min - lower_max
            gap_center = (upper_min + lower_max) / 2
            relative_gap = gap_size / gap_center if gap_center > 0 else 0
            
            complete_gaps.append({
                'bands': (i+1, i+2),
                'gap_size': gap_size,
                'relative_gap': relative_gap
            })
            
            print(f"  完整带隙 {i+1}-{i+2}: Δω = {gap_size:.4f}, 相对大小 = {relative_gap:.1%}")
    
    if not complete_gaps:
        print("  未发现完整带隙")
    
    # 绘制带结构
    plot_new_band_structure(bands, k_labels, k_label_positions, ra, epsilon_a)
    
    return bands, k_path, k_labels, k_label_positions

def plot_new_band_structure(bands, k_labels, k_label_positions, ra, epsilon_a):
    """绘制新路径的带结构"""
    plt.figure(figsize=(12, 8))
    
    # 绘制能带
    colors = ['blue', 'red', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']
    for i in range(bands.shape[1]):
        color = colors[i % len(colors)]
        plt.plot(range(len(bands)), bands[:, i], color=color, linewidth=1.5, label=f'Band {i+1}')
    
    # 设置x轴标签
    plt.xticks(k_label_positions, k_labels, fontsize=12)
    
    # 在高对称点处绘制垂直线
    for pos in k_label_positions:
        plt.axvline(x=pos, color='k', linestyle='-', alpha=0.3)
    
    # 标记带隙
    for i in range(bands.shape[1] - 1):
        lower_band = bands[:, i]
        upper_band = bands[:, i+1]
        
        lower_max = np.max(lower_band)
        upper_min = np.min(upper_band)
        
        if upper_min > lower_max:
            gap_size = upper_min - lower_max
            relative_gap = gap_size / ((upper_min + lower_max)/2)
            
            if relative_gap > 0.01:  # 显示1%以上的带隙
                plt.axhspan(lower_max, upper_min, alpha=0.3, color='yellow')
                plt.text(len(bands)*0.02, (lower_max + upper_min)/2, 
                        f'{relative_gap:.1%}', fontsize=8,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
    
    # 计算体积分数
    f = 4*np.pi*(ra**3)/3
    
    plt.xlabel('Wave Vector k', fontsize=12)
    plt.ylabel('Frequency ω/c', fontsize=12)
    plt.title(f'FCC密堆积光子晶体带结构\n路径: X→U→L→η→X→W→K\nra={ra:.5f}, εa={epsilon_a}, f={f:.4f}', fontsize=11)
    plt.grid(True, alpha=0.3)
    plt.legend(loc='upper right', fontsize=8)
    plt.tight_layout()
    
    # 保存图像
    plt.savefig('fcc_close_packed_band_structure.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    print("FCC密堆积光子晶体测试")
    print("=" * 50)
    
    try:
        # 测试FCC密堆积参数
        fcc_radius, fcc_volume_fraction = test_fcc_close_packed_parameters()
        
        # 测试新的对称性点路径
        high_sym_points, path_labels = test_new_symmetry_path()
        
        # 计算新路径的带结构
        bands, k_path, k_labels, k_label_positions = calculate_band_structure_new_path()
        
        print("\n" + "=" * 50)
        print("测试完成！")
        print(f"FCC密堆积半径: {fcc_radius:.6f}")
        print(f"新路径: {' → '.join(path_labels)}")
        print("带结构图已保存为 'fcc_close_packed_band_structure.png'")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
