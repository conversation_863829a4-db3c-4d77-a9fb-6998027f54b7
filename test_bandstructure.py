#!/usr/bin/env python3
"""
测试脚本：验证FCC光子晶体带结构计算的修改
"""

import numpy as np
import matplotlib.pyplot as plt
from photonic_band_calculator import (
    generate_fcc_reciprocal_lattice, 
    build_matrix, 
    solve_eigenvalue_problem,
    kappa_numba
)

def test_high_symmetry_points():
    """测试高对称点定义"""
    print("=== 测试高对称点定义 ===")
    
    # FCC结构的高对称点
    a = 2*np.pi
    high_sym_points = {
        "Γ": np.array([0.0, 0.0, 0.0]),
        "X": a*np.array([1.0, 0.0, 0.0]),
        "L": a*np.array([0.5, 0.5, 0.5]),
        "W": a*np.array([1.0, 0.5, 0.0]),
        "K": a*np.array([0.75, 0.75, 0.0]),
        "U": a*np.array([1.0, 0.25, 0.25])
    }
    
    for label, point in high_sym_points.items():
        print(f"{label}: ({point[0]:.3f}, {point[1]:.3f}, {point[2]:.3f})")
    
    return high_sym_points

def test_reciprocal_lattice():
    """测试倒格矢量生成"""
    print("\n=== 测试倒格矢量生成 ===")
    
    n_max = 2
    G_vectors = generate_fcc_reciprocal_lattice(n_max, use_cutoff=True)
    
    print(f"生成了 {len(G_vectors)} 个倒格矢量")
    print("前10个倒格矢量:")
    for i, G in enumerate(G_vectors[:10]):
        G_mag = np.linalg.norm(G)
        print(f"G[{i}]: ({G[0]:.3f}, {G[1]:.3f}, {G[2]:.3f}), |G| = {G_mag:.3f}")
    
    return G_vectors

def test_kappa_function():
    """测试kappa函数"""
    print("\n=== 测试kappa函数 ===")
    
    ra = 0.35
    epsilon_a = 12.0
    epsilon_b = 1.0
    f = 4*np.pi*(ra**3)/3
    
    # 测试G=0的情况
    G_zero = np.array([0.0, 0.0, 0.0])
    kappa_0 = kappa_numba(G_zero, ra, epsilon_a, epsilon_b, f)
    print(f"κ(G=0) = {kappa_0:.6f}")
    
    # 测试几个非零G矢量
    test_G_vectors = [
        np.array([1.0, 0.0, 0.0]),
        np.array([1.0, 1.0, 0.0]),
        np.array([1.0, 1.0, 1.0])
    ]
    
    for G in test_G_vectors:
        kappa_val = kappa_numba(G, ra, epsilon_a, epsilon_b, f)
        G_mag = np.linalg.norm(G)
        print(f"κ(G=({G[0]:.1f},{G[1]:.1f},{G[2]:.1f}), |G|={G_mag:.3f}) = {kappa_val:.6f}")

def test_band_calculation():
    """测试单点带结构计算"""
    print("\n=== 测试带结构计算 ===")
    
    # 参数设置
    ra = 0.35
    epsilon_a = 12.0
    epsilon_b = 1.0
    f = 4*np.pi*(ra**3)/3
    n_max = 3
    num_bands = 5
    
    print(f"参数: ra={ra}, εa={epsilon_a}, εb={epsilon_b}, f={f:.3f}")
    
    # 生成倒格矢量
    G_vectors = generate_fcc_reciprocal_lattice(n_max, use_cutoff=True)
    print(f"使用 {len(G_vectors)} 个平面波")
    
    # 测试几个高对称点
    a = 2*np.pi
    test_points = {
        "Γ": np.array([0.0, 0.0, 0.0]),
        "X": a*np.array([1.0, 0.0, 0.0]),
        "L": a*np.array([0.5, 0.5, 0.5])
    }
    
    for label, k_point in test_points.items():
        print(f"\n计算点 {label}: ({k_point[0]:.3f}, {k_point[1]:.3f}, {k_point[2]:.3f})")
        
        # 构建矩阵
        H = build_matrix(k_point, G_vectors, ra, epsilon_a, epsilon_b, f)
        
        # 求解特征值
        eigenvalues = solve_eigenvalue_problem(H, num_bands, "eigh")
        frequencies = np.sqrt(eigenvalues)
        
        print(f"频率 (ω/c): {frequencies}")
        
        # 检查是否有虚频率
        if np.any(np.imag(frequencies) != 0):
            print("警告: 发现虚频率!")
        
        # 检查频率排序
        if not np.all(frequencies[:-1] <= frequencies[1:]):
            print("警告: 频率未正确排序!")

def analyze_bandgap():
    """分析带隙特性"""
    print("\n=== 带隙分析 ===")
    
    # 参数设置
    ra = 0.35
    epsilon_a = 12.0
    epsilon_b = 1.0
    f = 4*np.pi*(ra**3)/3
    n_max = 3
    num_bands = 8
    
    # 生成倒格矢量
    G_vectors = generate_fcc_reciprocal_lattice(n_max, use_cutoff=True)
    
    # 计算Γ点的频率
    k_gamma = np.array([0.0, 0.0, 0.0])
    H_gamma = build_matrix(k_gamma, G_vectors, ra, epsilon_a, epsilon_b, f)
    eigenvalues_gamma = solve_eigenvalue_problem(H_gamma, num_bands, "eigh")
    frequencies_gamma = np.sqrt(eigenvalues_gamma)
    
    print(f"Γ点频率: {frequencies_gamma}")
    
    # 寻找带隙
    for i in range(len(frequencies_gamma) - 1):
        gap = frequencies_gamma[i+1] - frequencies_gamma[i]
        relative_gap = gap / frequencies_gamma[i] if frequencies_gamma[i] > 0 else 0
        print(f"带隙 {i+1}-{i+2}: Δω = {gap:.4f}, 相对大小 = {relative_gap:.1%}")

if __name__ == "__main__":
    print("FCC光子晶体带结构计算测试")
    print("=" * 50)
    
    try:
        test_high_symmetry_points()
        test_reciprocal_lattice()
        test_kappa_function()
        test_band_calculation()
        analyze_bandgap()
        
        print("\n" + "=" * 50)
        print("所有测试完成!")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
