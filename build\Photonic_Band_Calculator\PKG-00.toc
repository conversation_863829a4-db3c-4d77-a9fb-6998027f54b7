('G:\\wangchaoyu\\pwe-calculate\\build\\Photonic_Band_Calculator\\PhotonicBandCalculator.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'G:\\wangchaoyu\\pwe-calculate\\build\\Photonic_Band_Calculator\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'G:\\wangchaoyu\\pwe-calculate\\build\\Photonic_Band_Calculator\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'G:\\wangchaoyu\\pwe-calculate\\build\\Photonic_Band_Calculator\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'G:\\wangchaoyu\\pwe-calculate\\build\\Photonic_Band_Calculator\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'G:\\wangchaoyu\\pwe-calculate\\build\\Photonic_Band_Calculator\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'G:\\wangchaoyu\\pwe-calculate\\build\\Photonic_Band_Calculator\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Python\\Python310\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Python\\Python310\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Python\\Python310\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Python\\Python310\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Python\\Python310\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_mplconfig',
   'C:\\Python\\Python310\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_mplconfig.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Python\\Python310\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\Python\\Python310\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('photonic_band_calculator',
   'G:\\wangchaoyu\\pwe-calculate\\photonic_band_calculator.py',
   'PYSOURCE')],
 'python310.dll',
 True,
 False,
 False,
 [],
 None,
 None,
 None)
