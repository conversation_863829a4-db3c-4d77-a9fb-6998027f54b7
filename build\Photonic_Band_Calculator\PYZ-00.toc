('G:\\wangchaoyu\\pwe-calculate\\build\\Photonic_Band_Calculator\\PYZ-00.pyz',
 [('PIL',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Python\\Python310\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__', 'C:\\Python\\Python310\\lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'C:\\Python\\Python310\\lib\\_aix_support.py', 'PYMODULE'),
  ('_bootsubprocess',
   'C:\\Python\\Python310\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Python\\Python310\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression', 'C:\\Python\\Python310\\lib\\_compression.py', 'PYMODULE'),
  ('_distutils_hack',
   'C:\\Python\\Python310\\lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Python\\Python310\\lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_osx_support', 'C:\\Python\\Python310\\lib\\_osx_support.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Python\\Python310\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Python\\Python310\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\Python\\Python310\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils._win32',
   'C:\\Python\\Python310\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\_win32.py',
   'PYMODULE'),
  ('_pyi_rth_utils.tempfile',
   'C:\\Python\\Python310\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\tempfile.py',
   'PYMODULE'),
  ('_sitebuiltins', 'C:\\Python\\Python310\\lib\\_sitebuiltins.py', 'PYMODULE'),
  ('_strptime', 'C:\\Python\\Python310\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Python\\Python310\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'C:\\Python\\Python310\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\Python\\Python310\\lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'C:\\Python\\Python310\\lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Python\\Python310\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Python\\Python310\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Python\\Python310\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Python\\Python310\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Python\\Python310\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Python\\Python310\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Python\\Python310\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Python\\Python310\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Python\\Python310\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Python\\Python310\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Python\\Python310\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log', 'C:\\Python\\Python310\\lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Python\\Python310\\lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Python\\Python310\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Python\\Python310\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Python\\Python310\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Python\\Python310\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Python\\Python310\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Python\\Python310\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Python\\Python310\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Python\\Python310\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Python\\Python310\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Python\\Python310\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Python\\Python310\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Python\\Python310\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Python\\Python310\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Python\\Python310\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Python\\Python310\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Python\\Python310\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'C:\\Python\\Python310\\lib\\base64.py', 'PYMODULE'),
  ('bdb', 'C:\\Python\\Python310\\lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'C:\\Python\\Python310\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'C:\\Python\\Python310\\lib\\bz2.py', 'PYMODULE'),
  ('cProfile', 'C:\\Python\\Python310\\lib\\cProfile.py', 'PYMODULE'),
  ('calendar', 'C:\\Python\\Python310\\lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'C:\\Python\\Python310\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Python\\Python310\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'C:\\Python\\Python310\\lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cgi', 'C:\\Python\\Python310\\lib\\cgi.py', 'PYMODULE'),
  ('charset_normalizer',
   'C:\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Python\\Python310\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd', 'C:\\Python\\Python310\\lib\\cmd.py', 'PYMODULE'),
  ('code', 'C:\\Python\\Python310\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Python\\Python310\\lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'C:\\Python\\Python310\\lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'C:\\Python\\Python310\\lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'C:\\Python\\Python310\\lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'C:\\Python\\Python310\\lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'C:\\Python\\Python310\\lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'C:\\Python\\Python310\\lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys', 'C:\\Python\\Python310\\lib\\colorsys.py', 'PYMODULE'),
  ('concurrent',
   'C:\\Python\\Python310\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Python\\Python310\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Python\\Python310\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Python\\Python310\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Python\\Python310\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'C:\\Python\\Python310\\lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'C:\\Python\\Python310\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'C:\\Python\\Python310\\lib\\contextvars.py', 'PYMODULE'),
  ('contourpy',
   'C:\\Python\\Python310\\lib\\site-packages\\contourpy\\__init__.py',
   'PYMODULE'),
  ('contourpy._version',
   'C:\\Python\\Python310\\lib\\site-packages\\contourpy\\_version.py',
   'PYMODULE'),
  ('contourpy.array',
   'C:\\Python\\Python310\\lib\\site-packages\\contourpy\\array.py',
   'PYMODULE'),
  ('contourpy.chunk',
   'C:\\Python\\Python310\\lib\\site-packages\\contourpy\\chunk.py',
   'PYMODULE'),
  ('contourpy.convert',
   'C:\\Python\\Python310\\lib\\site-packages\\contourpy\\convert.py',
   'PYMODULE'),
  ('contourpy.dechunk',
   'C:\\Python\\Python310\\lib\\site-packages\\contourpy\\dechunk.py',
   'PYMODULE'),
  ('contourpy.enum_util',
   'C:\\Python\\Python310\\lib\\site-packages\\contourpy\\enum_util.py',
   'PYMODULE'),
  ('contourpy.typecheck',
   'C:\\Python\\Python310\\lib\\site-packages\\contourpy\\typecheck.py',
   'PYMODULE'),
  ('contourpy.types',
   'C:\\Python\\Python310\\lib\\site-packages\\contourpy\\types.py',
   'PYMODULE'),
  ('copy', 'C:\\Python\\Python310\\lib\\copy.py', 'PYMODULE'),
  ('csv', 'C:\\Python\\Python310\\lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'C:\\Python\\Python310\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'C:\\Python\\Python310\\lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian',
   'C:\\Python\\Python310\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Python\\Python310\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Python\\Python310\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Python\\Python310\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Python\\Python310\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'C:\\Python\\Python310\\lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Python\\Python310\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('cycler',
   'C:\\Python\\Python310\\lib\\site-packages\\cycler\\__init__.py',
   'PYMODULE'),
  ('dataclasses', 'C:\\Python\\Python310\\lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'C:\\Python\\Python310\\lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'C:\\Python\\Python310\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal', 'C:\\Python\\Python310\\lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'C:\\Python\\Python310\\lib\\difflib.py', 'PYMODULE'),
  ('dis', 'C:\\Python\\Python310\\lib\\dis.py', 'PYMODULE'),
  ('distutils',
   'C:\\Python\\Python310\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'C:\\Python\\Python310\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'C:\\Python\\Python310\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'C:\\Python\\Python310\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd',
   'C:\\Python\\Python310\\lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.command',
   'C:\\Python\\Python310\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'C:\\Python\\Python310\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'C:\\Python\\Python310\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'C:\\Python\\Python310\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config',
   'C:\\Python\\Python310\\lib\\distutils\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'C:\\Python\\Python310\\lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.debug',
   'C:\\Python\\Python310\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'C:\\Python\\Python310\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'C:\\Python\\Python310\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.dist',
   'C:\\Python\\Python310\\lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.errors',
   'C:\\Python\\Python310\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.extension',
   'C:\\Python\\Python310\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'C:\\Python\\Python310\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'C:\\Python\\Python310\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'C:\\Python\\Python310\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.log',
   'C:\\Python\\Python310\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'C:\\Python\\Python310\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.spawn',
   'C:\\Python\\Python310\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'C:\\Python\\Python310\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'C:\\Python\\Python310\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.unixccompiler',
   'C:\\Python\\Python310\\lib\\distutils\\unixccompiler.py',
   'PYMODULE'),
  ('distutils.util',
   'C:\\Python\\Python310\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.version',
   'C:\\Python\\Python310\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'C:\\Python\\Python310\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('doctest', 'C:\\Python\\Python310\\lib\\doctest.py', 'PYMODULE'),
  ('email', 'C:\\Python\\Python310\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Python\\Python310\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Python\\Python310\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Python\\Python310\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Python\\Python310\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Python\\Python310\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Python\\Python310\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Python\\Python310\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Python\\Python310\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors', 'C:\\Python\\Python310\\lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser',
   'C:\\Python\\Python310\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Python\\Python310\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header', 'C:\\Python\\Python310\\lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'C:\\Python\\Python310\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Python\\Python310\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Python\\Python310\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser', 'C:\\Python\\Python310\\lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'C:\\Python\\Python310\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime',
   'C:\\Python\\Python310\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils', 'C:\\Python\\Python310\\lib\\email\\utils.py', 'PYMODULE'),
  ('fileinput', 'C:\\Python\\Python310\\lib\\fileinput.py', 'PYMODULE'),
  ('fnmatch', 'C:\\Python\\Python310\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\Python\\Python310\\lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'C:\\Python\\Python310\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'C:\\Python\\Python310\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'C:\\Python\\Python310\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'C:\\Python\\Python310\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'C:\\Python\\Python310\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'C:\\Python\\Python310\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\Python\\Python310\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'C:\\Python\\Python310\\lib\\hmac.py', 'PYMODULE'),
  ('html', 'C:\\Python\\Python310\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'C:\\Python\\Python310\\lib\\html\\entities.py',
   'PYMODULE'),
  ('http', 'C:\\Python\\Python310\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'C:\\Python\\Python310\\lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar',
   'C:\\Python\\Python310\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server', 'C:\\Python\\Python310\\lib\\http\\server.py', 'PYMODULE'),
  ('imp', 'C:\\Python\\Python310\\lib\\imp.py', 'PYMODULE'),
  ('importlib',
   'C:\\Python\\Python310\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Python\\Python310\\lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._adapters',
   'C:\\Python\\Python310\\lib\\importlib\\_adapters.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Python\\Python310\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Python\\Python310\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._common',
   'C:\\Python\\Python310\\lib\\importlib\\_common.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Python\\Python310\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Python\\Python310\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Python\\Python310\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Python\\Python310\\lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Python\\Python310\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Python\\Python310\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'C:\\Python\\Python310\\lib\\inspect.py', 'PYMODULE'),
  ('jinja2',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'C:\\Python\\Python310\\lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json', 'C:\\Python\\Python310\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'C:\\Python\\Python310\\lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'C:\\Python\\Python310\\lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'C:\\Python\\Python310\\lib\\json\\scanner.py', 'PYMODULE'),
  ('kiwisolver',
   'C:\\Python\\Python310\\lib\\site-packages\\kiwisolver\\__init__.py',
   'PYMODULE'),
  ('kiwisolver.exceptions',
   'C:\\Python\\Python310\\lib\\site-packages\\kiwisolver\\exceptions.py',
   'PYMODULE'),
  ('llvmlite',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\__init__.py',
   'PYMODULE'),
  ('llvmlite._version',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\_version.py',
   'PYMODULE'),
  ('llvmlite.binding',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\__init__.py',
   'PYMODULE'),
  ('llvmlite.binding.analysis',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\analysis.py',
   'PYMODULE'),
  ('llvmlite.binding.common',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\common.py',
   'PYMODULE'),
  ('llvmlite.binding.context',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\context.py',
   'PYMODULE'),
  ('llvmlite.binding.dylib',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\dylib.py',
   'PYMODULE'),
  ('llvmlite.binding.executionengine',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\executionengine.py',
   'PYMODULE'),
  ('llvmlite.binding.ffi',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\ffi.py',
   'PYMODULE'),
  ('llvmlite.binding.initfini',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\initfini.py',
   'PYMODULE'),
  ('llvmlite.binding.linker',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\linker.py',
   'PYMODULE'),
  ('llvmlite.binding.module',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\module.py',
   'PYMODULE'),
  ('llvmlite.binding.newpassmanagers',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\newpassmanagers.py',
   'PYMODULE'),
  ('llvmlite.binding.object_file',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\object_file.py',
   'PYMODULE'),
  ('llvmlite.binding.options',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\options.py',
   'PYMODULE'),
  ('llvmlite.binding.orcjit',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\orcjit.py',
   'PYMODULE'),
  ('llvmlite.binding.passmanagers',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\passmanagers.py',
   'PYMODULE'),
  ('llvmlite.binding.targets',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\targets.py',
   'PYMODULE'),
  ('llvmlite.binding.transforms',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\transforms.py',
   'PYMODULE'),
  ('llvmlite.binding.typeref',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\typeref.py',
   'PYMODULE'),
  ('llvmlite.binding.value',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\binding\\value.py',
   'PYMODULE'),
  ('llvmlite.ir',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\ir\\__init__.py',
   'PYMODULE'),
  ('llvmlite.ir._utils',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\ir\\_utils.py',
   'PYMODULE'),
  ('llvmlite.ir.builder',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\ir\\builder.py',
   'PYMODULE'),
  ('llvmlite.ir.context',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\ir\\context.py',
   'PYMODULE'),
  ('llvmlite.ir.instructions',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\ir\\instructions.py',
   'PYMODULE'),
  ('llvmlite.ir.module',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\ir\\module.py',
   'PYMODULE'),
  ('llvmlite.ir.transforms',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\ir\\transforms.py',
   'PYMODULE'),
  ('llvmlite.ir.types',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\ir\\types.py',
   'PYMODULE'),
  ('llvmlite.ir.values',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\ir\\values.py',
   'PYMODULE'),
  ('llvmlite.utils',
   'C:\\Python\\Python310\\lib\\site-packages\\llvmlite\\utils.py',
   'PYMODULE'),
  ('logging', 'C:\\Python\\Python310\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'C:\\Python\\Python310\\lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'C:\\Python\\Python310\\lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'C:\\Python\\Python310\\lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('matplotlib',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\__init__.py',
   'PYMODULE'),
  ('matplotlib._afm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_afm.py',
   'PYMODULE'),
  ('matplotlib._api',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_api\\__init__.py',
   'PYMODULE'),
  ('matplotlib._api.deprecation',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_api\\deprecation.py',
   'PYMODULE'),
  ('matplotlib._blocking_input',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_blocking_input.py',
   'PYMODULE'),
  ('matplotlib._cm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_cm.py',
   'PYMODULE'),
  ('matplotlib._cm_bivar',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_cm_bivar.py',
   'PYMODULE'),
  ('matplotlib._cm_listed',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_cm_listed.py',
   'PYMODULE'),
  ('matplotlib._cm_multivar',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_cm_multivar.py',
   'PYMODULE'),
  ('matplotlib._color_data',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_color_data.py',
   'PYMODULE'),
  ('matplotlib._constrained_layout',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_constrained_layout.py',
   'PYMODULE'),
  ('matplotlib._docstring',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_docstring.py',
   'PYMODULE'),
  ('matplotlib._enums',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_enums.py',
   'PYMODULE'),
  ('matplotlib._fontconfig_pattern',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_fontconfig_pattern.py',
   'PYMODULE'),
  ('matplotlib._layoutgrid',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_layoutgrid.py',
   'PYMODULE'),
  ('matplotlib._mathtext',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_mathtext.py',
   'PYMODULE'),
  ('matplotlib._mathtext_data',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_mathtext_data.py',
   'PYMODULE'),
  ('matplotlib._pylab_helpers',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_pylab_helpers.py',
   'PYMODULE'),
  ('matplotlib._text_helpers',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_text_helpers.py',
   'PYMODULE'),
  ('matplotlib._tight_bbox',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_tight_bbox.py',
   'PYMODULE'),
  ('matplotlib._tight_layout',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_tight_layout.py',
   'PYMODULE'),
  ('matplotlib._version',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\_version.py',
   'PYMODULE'),
  ('matplotlib.artist',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\artist.py',
   'PYMODULE'),
  ('matplotlib.axes',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\axes\\__init__.py',
   'PYMODULE'),
  ('matplotlib.axes._axes',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\axes\\_axes.py',
   'PYMODULE'),
  ('matplotlib.axes._base',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\axes\\_base.py',
   'PYMODULE'),
  ('matplotlib.axes._secondary_axes',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\axes\\_secondary_axes.py',
   'PYMODULE'),
  ('matplotlib.axis',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\axis.py',
   'PYMODULE'),
  ('matplotlib.backend_bases',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\backend_bases.py',
   'PYMODULE'),
  ('matplotlib.backend_managers',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\backend_managers.py',
   'PYMODULE'),
  ('matplotlib.backend_tools',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\backend_tools.py',
   'PYMODULE'),
  ('matplotlib.backends',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\backends\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends._backend_tk',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\backends\\_backend_tk.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_agg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\backends\\backend_agg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_tkagg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\backends\\backend_tkagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\backends\\backend_webagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg_core',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\backends\\backend_webagg_core.py',
   'PYMODULE'),
  ('matplotlib.backends.registry',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\backends\\registry.py',
   'PYMODULE'),
  ('matplotlib.bezier',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\bezier.py',
   'PYMODULE'),
  ('matplotlib.category',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\category.py',
   'PYMODULE'),
  ('matplotlib.cbook',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\cbook.py',
   'PYMODULE'),
  ('matplotlib.cm',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\cm.py',
   'PYMODULE'),
  ('matplotlib.collections',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\collections.py',
   'PYMODULE'),
  ('matplotlib.colorbar',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\colorbar.py',
   'PYMODULE'),
  ('matplotlib.colorizer',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\colorizer.py',
   'PYMODULE'),
  ('matplotlib.colors',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\colors.py',
   'PYMODULE'),
  ('matplotlib.container',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\container.py',
   'PYMODULE'),
  ('matplotlib.contour',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\contour.py',
   'PYMODULE'),
  ('matplotlib.dates',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\dates.py',
   'PYMODULE'),
  ('matplotlib.dviread',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\dviread.py',
   'PYMODULE'),
  ('matplotlib.figure',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\figure.py',
   'PYMODULE'),
  ('matplotlib.font_manager',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\font_manager.py',
   'PYMODULE'),
  ('matplotlib.gridspec',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\gridspec.py',
   'PYMODULE'),
  ('matplotlib.hatch',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\hatch.py',
   'PYMODULE'),
  ('matplotlib.image',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\image.py',
   'PYMODULE'),
  ('matplotlib.inset',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\inset.py',
   'PYMODULE'),
  ('matplotlib.layout_engine',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\layout_engine.py',
   'PYMODULE'),
  ('matplotlib.legend',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\legend.py',
   'PYMODULE'),
  ('matplotlib.legend_handler',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\legend_handler.py',
   'PYMODULE'),
  ('matplotlib.lines',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\lines.py',
   'PYMODULE'),
  ('matplotlib.markers',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\markers.py',
   'PYMODULE'),
  ('matplotlib.mathtext',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mathtext.py',
   'PYMODULE'),
  ('matplotlib.mlab',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\mlab.py',
   'PYMODULE'),
  ('matplotlib.offsetbox',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\offsetbox.py',
   'PYMODULE'),
  ('matplotlib.patches',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\patches.py',
   'PYMODULE'),
  ('matplotlib.path',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\path.py',
   'PYMODULE'),
  ('matplotlib.patheffects',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\patheffects.py',
   'PYMODULE'),
  ('matplotlib.projections',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\projections\\__init__.py',
   'PYMODULE'),
  ('matplotlib.projections.geo',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\projections\\geo.py',
   'PYMODULE'),
  ('matplotlib.projections.polar',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\projections\\polar.py',
   'PYMODULE'),
  ('matplotlib.pyplot',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\pyplot.py',
   'PYMODULE'),
  ('matplotlib.quiver',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\quiver.py',
   'PYMODULE'),
  ('matplotlib.rcsetup',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\rcsetup.py',
   'PYMODULE'),
  ('matplotlib.scale',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\scale.py',
   'PYMODULE'),
  ('matplotlib.spines',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\spines.py',
   'PYMODULE'),
  ('matplotlib.stackplot',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\stackplot.py',
   'PYMODULE'),
  ('matplotlib.streamplot',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\streamplot.py',
   'PYMODULE'),
  ('matplotlib.style',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\style\\__init__.py',
   'PYMODULE'),
  ('matplotlib.style.core',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\style\\core.py',
   'PYMODULE'),
  ('matplotlib.table',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\table.py',
   'PYMODULE'),
  ('matplotlib.texmanager',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\texmanager.py',
   'PYMODULE'),
  ('matplotlib.text',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\text.py',
   'PYMODULE'),
  ('matplotlib.textpath',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\textpath.py',
   'PYMODULE'),
  ('matplotlib.ticker',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\ticker.py',
   'PYMODULE'),
  ('matplotlib.transforms',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\transforms.py',
   'PYMODULE'),
  ('matplotlib.tri',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\tri\\__init__.py',
   'PYMODULE'),
  ('matplotlib.tri._triangulation',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\tri\\_triangulation.py',
   'PYMODULE'),
  ('matplotlib.tri._tricontour',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\tri\\_tricontour.py',
   'PYMODULE'),
  ('matplotlib.tri._trifinder',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\tri\\_trifinder.py',
   'PYMODULE'),
  ('matplotlib.tri._triinterpolate',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\tri\\_triinterpolate.py',
   'PYMODULE'),
  ('matplotlib.tri._tripcolor',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\tri\\_tripcolor.py',
   'PYMODULE'),
  ('matplotlib.tri._triplot',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\tri\\_triplot.py',
   'PYMODULE'),
  ('matplotlib.tri._trirefine',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\tri\\_trirefine.py',
   'PYMODULE'),
  ('matplotlib.tri._tritools',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\tri\\_tritools.py',
   'PYMODULE'),
  ('matplotlib.typing',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\typing.py',
   'PYMODULE'),
  ('matplotlib.units',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\units.py',
   'PYMODULE'),
  ('matplotlib.widgets',
   'C:\\Python\\Python310\\lib\\site-packages\\matplotlib\\widgets.py',
   'PYMODULE'),
  ('mimetypes', 'C:\\Python\\Python310\\lib\\mimetypes.py', 'PYMODULE'),
  ('mpl_toolkits', '-', 'PYMODULE'),
  ('mpl_toolkits.mplot3d',
   'C:\\Python\\Python310\\lib\\site-packages\\mpl_toolkits\\mplot3d\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.art3d',
   'C:\\Python\\Python310\\lib\\site-packages\\mpl_toolkits\\mplot3d\\art3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axes3d',
   'C:\\Python\\Python310\\lib\\site-packages\\mpl_toolkits\\mplot3d\\axes3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axis3d',
   'C:\\Python\\Python310\\lib\\site-packages\\mpl_toolkits\\mplot3d\\axis3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.proj3d',
   'C:\\Python\\Python310\\lib\\site-packages\\mpl_toolkits\\mplot3d\\proj3d.py',
   'PYMODULE'),
  ('msilib', 'C:\\Python\\Python310\\lib\\msilib\\__init__.py', 'PYMODULE'),
  ('msilib.schema',
   'C:\\Python\\Python310\\lib\\msilib\\schema.py',
   'PYMODULE'),
  ('msilib.sequence',
   'C:\\Python\\Python310\\lib\\msilib\\sequence.py',
   'PYMODULE'),
  ('msilib.text', 'C:\\Python\\Python310\\lib\\msilib\\text.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\Python\\Python310\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Python\\Python310\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Python\\Python310\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Python\\Python310\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Python\\Python310\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Python\\Python310\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Python\\Python310\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Python\\Python310\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Python\\Python310\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Python\\Python310\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Python\\Python310\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Python\\Python310\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Python\\Python310\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Python\\Python310\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Python\\Python310\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Python\\Python310\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Python\\Python310\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Python\\Python310\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Python\\Python310\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Python\\Python310\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Python\\Python310\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'C:\\Python\\Python310\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'C:\\Python\\Python310\\lib\\nturl2path.py', 'PYMODULE'),
  ('numba',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\__init__.py',
   'PYMODULE'),
  ('numba._version',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\_version.py',
   'PYMODULE'),
  ('numba.cext',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cext\\__init__.py',
   'PYMODULE'),
  ('numba.cloudpickle',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cloudpickle\\__init__.py',
   'PYMODULE'),
  ('numba.cloudpickle.cloudpickle',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cloudpickle\\cloudpickle.py',
   'PYMODULE'),
  ('numba.cloudpickle.cloudpickle_fast',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cloudpickle\\cloudpickle_fast.py',
   'PYMODULE'),
  ('numba.core',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\__init__.py',
   'PYMODULE'),
  ('numba.core.analysis',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\analysis.py',
   'PYMODULE'),
  ('numba.core.annotations',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\annotations\\__init__.py',
   'PYMODULE'),
  ('numba.core.annotations.pretty_annotate',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\annotations\\pretty_annotate.py',
   'PYMODULE'),
  ('numba.core.annotations.type_annotations',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\annotations\\type_annotations.py',
   'PYMODULE'),
  ('numba.core.base',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\base.py',
   'PYMODULE'),
  ('numba.core.boxing',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\boxing.py',
   'PYMODULE'),
  ('numba.core.bytecode',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\bytecode.py',
   'PYMODULE'),
  ('numba.core.byteflow',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\byteflow.py',
   'PYMODULE'),
  ('numba.core.caching',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\caching.py',
   'PYMODULE'),
  ('numba.core.callconv',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\callconv.py',
   'PYMODULE'),
  ('numba.core.callwrapper',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\callwrapper.py',
   'PYMODULE'),
  ('numba.core.ccallback',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\ccallback.py',
   'PYMODULE'),
  ('numba.core.cgutils',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\cgutils.py',
   'PYMODULE'),
  ('numba.core.codegen',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\codegen.py',
   'PYMODULE'),
  ('numba.core.compiler',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\compiler.py',
   'PYMODULE'),
  ('numba.core.compiler_lock',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\compiler_lock.py',
   'PYMODULE'),
  ('numba.core.compiler_machinery',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\compiler_machinery.py',
   'PYMODULE'),
  ('numba.core.config',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\config.py',
   'PYMODULE'),
  ('numba.core.consts',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\consts.py',
   'PYMODULE'),
  ('numba.core.controlflow',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\controlflow.py',
   'PYMODULE'),
  ('numba.core.cpu',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\cpu.py',
   'PYMODULE'),
  ('numba.core.cpu_options',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\cpu_options.py',
   'PYMODULE'),
  ('numba.core.datamodel',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\datamodel\\__init__.py',
   'PYMODULE'),
  ('numba.core.datamodel.manager',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\datamodel\\manager.py',
   'PYMODULE'),
  ('numba.core.datamodel.models',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\datamodel\\models.py',
   'PYMODULE'),
  ('numba.core.datamodel.new_models',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\datamodel\\new_models.py',
   'PYMODULE'),
  ('numba.core.datamodel.old_models',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\datamodel\\old_models.py',
   'PYMODULE'),
  ('numba.core.datamodel.packer',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\datamodel\\packer.py',
   'PYMODULE'),
  ('numba.core.datamodel.registry',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\datamodel\\registry.py',
   'PYMODULE'),
  ('numba.core.debuginfo',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\debuginfo.py',
   'PYMODULE'),
  ('numba.core.decorators',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\decorators.py',
   'PYMODULE'),
  ('numba.core.descriptors',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\descriptors.py',
   'PYMODULE'),
  ('numba.core.dispatcher',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\dispatcher.py',
   'PYMODULE'),
  ('numba.core.entrypoints',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\entrypoints.py',
   'PYMODULE'),
  ('numba.core.environment',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\environment.py',
   'PYMODULE'),
  ('numba.core.errors',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\errors.py',
   'PYMODULE'),
  ('numba.core.event',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\event.py',
   'PYMODULE'),
  ('numba.core.extending',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\extending.py',
   'PYMODULE'),
  ('numba.core.externals',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\externals.py',
   'PYMODULE'),
  ('numba.core.fastmathpass',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\fastmathpass.py',
   'PYMODULE'),
  ('numba.core.funcdesc',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\funcdesc.py',
   'PYMODULE'),
  ('numba.core.generators',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\generators.py',
   'PYMODULE'),
  ('numba.core.imputils',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\imputils.py',
   'PYMODULE'),
  ('numba.core.inline_closurecall',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\inline_closurecall.py',
   'PYMODULE'),
  ('numba.core.interpreter',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\interpreter.py',
   'PYMODULE'),
  ('numba.core.intrinsics',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\intrinsics.py',
   'PYMODULE'),
  ('numba.core.ir',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\ir.py',
   'PYMODULE'),
  ('numba.core.ir_utils',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\ir_utils.py',
   'PYMODULE'),
  ('numba.core.itanium_mangler',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\itanium_mangler.py',
   'PYMODULE'),
  ('numba.core.llvm_bindings',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\llvm_bindings.py',
   'PYMODULE'),
  ('numba.core.lowering',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\lowering.py',
   'PYMODULE'),
  ('numba.core.new_boxing',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\new_boxing.py',
   'PYMODULE'),
  ('numba.core.object_mode_passes',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\object_mode_passes.py',
   'PYMODULE'),
  ('numba.core.old_boxing',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\old_boxing.py',
   'PYMODULE'),
  ('numba.core.optional',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\optional.py',
   'PYMODULE'),
  ('numba.core.options',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\options.py',
   'PYMODULE'),
  ('numba.core.postproc',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\postproc.py',
   'PYMODULE'),
  ('numba.core.pylowering',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\pylowering.py',
   'PYMODULE'),
  ('numba.core.pythonapi',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\pythonapi.py',
   'PYMODULE'),
  ('numba.core.registry',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\registry.py',
   'PYMODULE'),
  ('numba.core.removerefctpass',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\removerefctpass.py',
   'PYMODULE'),
  ('numba.core.rewrites',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\rewrites\\__init__.py',
   'PYMODULE'),
  ('numba.core.rewrites.ir_print',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\rewrites\\ir_print.py',
   'PYMODULE'),
  ('numba.core.rewrites.registry',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\rewrites\\registry.py',
   'PYMODULE'),
  ('numba.core.rewrites.static_binop',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\rewrites\\static_binop.py',
   'PYMODULE'),
  ('numba.core.rewrites.static_getitem',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\rewrites\\static_getitem.py',
   'PYMODULE'),
  ('numba.core.rewrites.static_raise',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\rewrites\\static_raise.py',
   'PYMODULE'),
  ('numba.core.runtime',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\runtime\\__init__.py',
   'PYMODULE'),
  ('numba.core.runtime.context',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\runtime\\context.py',
   'PYMODULE'),
  ('numba.core.runtime.nrt',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\runtime\\nrt.py',
   'PYMODULE'),
  ('numba.core.runtime.nrtdynmod',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\runtime\\nrtdynmod.py',
   'PYMODULE'),
  ('numba.core.runtime.nrtopt',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\runtime\\nrtopt.py',
   'PYMODULE'),
  ('numba.core.serialize',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\serialize.py',
   'PYMODULE'),
  ('numba.core.sigutils',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\sigutils.py',
   'PYMODULE'),
  ('numba.core.ssa',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\ssa.py',
   'PYMODULE'),
  ('numba.core.target_extension',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\target_extension.py',
   'PYMODULE'),
  ('numba.core.targetconfig',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\targetconfig.py',
   'PYMODULE'),
  ('numba.core.tracing',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\tracing.py',
   'PYMODULE'),
  ('numba.core.transforms',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\transforms.py',
   'PYMODULE'),
  ('numba.core.typeconv',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typeconv\\__init__.py',
   'PYMODULE'),
  ('numba.core.typeconv.castgraph',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typeconv\\castgraph.py',
   'PYMODULE'),
  ('numba.core.typeconv.rules',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typeconv\\rules.py',
   'PYMODULE'),
  ('numba.core.typeconv.typeconv',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typeconv\\typeconv.py',
   'PYMODULE'),
  ('numba.core.typed_passes',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typed_passes.py',
   'PYMODULE'),
  ('numba.core.typeinfer',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typeinfer.py',
   'PYMODULE'),
  ('numba.core.types',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\__init__.py',
   'PYMODULE'),
  ('numba.core.types.abstract',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\abstract.py',
   'PYMODULE'),
  ('numba.core.types.common',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\common.py',
   'PYMODULE'),
  ('numba.core.types.containers',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\containers.py',
   'PYMODULE'),
  ('numba.core.types.function_type',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\function_type.py',
   'PYMODULE'),
  ('numba.core.types.functions',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\functions.py',
   'PYMODULE'),
  ('numba.core.types.iterators',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\iterators.py',
   'PYMODULE'),
  ('numba.core.types.misc',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\misc.py',
   'PYMODULE'),
  ('numba.core.types.new_scalars',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\new_scalars\\__init__.py',
   'PYMODULE'),
  ('numba.core.types.new_scalars.machine_types',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\new_scalars\\machine_types.py',
   'PYMODULE'),
  ('numba.core.types.new_scalars.numpy_types',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\new_scalars\\numpy_types.py',
   'PYMODULE'),
  ('numba.core.types.new_scalars.python_types',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\new_scalars\\python_types.py',
   'PYMODULE'),
  ('numba.core.types.new_scalars.scalars',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\new_scalars\\scalars.py',
   'PYMODULE'),
  ('numba.core.types.npytypes',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\npytypes.py',
   'PYMODULE'),
  ('numba.core.types.old_scalars',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\old_scalars.py',
   'PYMODULE'),
  ('numba.core.types.scalars',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\types\\scalars.py',
   'PYMODULE'),
  ('numba.core.typing',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\__init__.py',
   'PYMODULE'),
  ('numba.core.typing.arraydecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\arraydecl.py',
   'PYMODULE'),
  ('numba.core.typing.asnumbatype',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\asnumbatype.py',
   'PYMODULE'),
  ('numba.core.typing.bufproto',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\bufproto.py',
   'PYMODULE'),
  ('numba.core.typing.builtins',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\builtins.py',
   'PYMODULE'),
  ('numba.core.typing.cffi_utils',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\cffi_utils.py',
   'PYMODULE'),
  ('numba.core.typing.cmathdecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\cmathdecl.py',
   'PYMODULE'),
  ('numba.core.typing.collections',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\collections.py',
   'PYMODULE'),
  ('numba.core.typing.context',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\context.py',
   'PYMODULE'),
  ('numba.core.typing.ctypes_utils',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\ctypes_utils.py',
   'PYMODULE'),
  ('numba.core.typing.dictdecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\dictdecl.py',
   'PYMODULE'),
  ('numba.core.typing.enumdecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\enumdecl.py',
   'PYMODULE'),
  ('numba.core.typing.listdecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\listdecl.py',
   'PYMODULE'),
  ('numba.core.typing.mathdecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\mathdecl.py',
   'PYMODULE'),
  ('numba.core.typing.new_builtins',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\new_builtins.py',
   'PYMODULE'),
  ('numba.core.typing.new_cmathdecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\new_cmathdecl.py',
   'PYMODULE'),
  ('numba.core.typing.new_mathdecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\new_mathdecl.py',
   'PYMODULE'),
  ('numba.core.typing.npdatetime',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\npdatetime.py',
   'PYMODULE'),
  ('numba.core.typing.npydecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\npydecl.py',
   'PYMODULE'),
  ('numba.core.typing.old_builtins',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\old_builtins.py',
   'PYMODULE'),
  ('numba.core.typing.old_cmathdecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\old_cmathdecl.py',
   'PYMODULE'),
  ('numba.core.typing.old_mathdecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\old_mathdecl.py',
   'PYMODULE'),
  ('numba.core.typing.setdecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\setdecl.py',
   'PYMODULE'),
  ('numba.core.typing.templates',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\templates.py',
   'PYMODULE'),
  ('numba.core.typing.typeof',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\typing\\typeof.py',
   'PYMODULE'),
  ('numba.core.unsafe',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\unsafe\\__init__.py',
   'PYMODULE'),
  ('numba.core.unsafe.bytes',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\unsafe\\bytes.py',
   'PYMODULE'),
  ('numba.core.unsafe.eh',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\unsafe\\eh.py',
   'PYMODULE'),
  ('numba.core.untyped_passes',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\untyped_passes.py',
   'PYMODULE'),
  ('numba.core.utils',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\utils.py',
   'PYMODULE'),
  ('numba.core.withcontexts',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\core\\withcontexts.py',
   'PYMODULE'),
  ('numba.cpython',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\__init__.py',
   'PYMODULE'),
  ('numba.cpython.builtins',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\builtins.py',
   'PYMODULE'),
  ('numba.cpython.charseq',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\charseq.py',
   'PYMODULE'),
  ('numba.cpython.cmathimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\cmathimpl.py',
   'PYMODULE'),
  ('numba.cpython.enumimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\enumimpl.py',
   'PYMODULE'),
  ('numba.cpython.hashing',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\hashing.py',
   'PYMODULE'),
  ('numba.cpython.heapq',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\heapq.py',
   'PYMODULE'),
  ('numba.cpython.iterators',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\iterators.py',
   'PYMODULE'),
  ('numba.cpython.listobj',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\listobj.py',
   'PYMODULE'),
  ('numba.cpython.mathimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\mathimpl.py',
   'PYMODULE'),
  ('numba.cpython.new_builtins',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\new_builtins.py',
   'PYMODULE'),
  ('numba.cpython.new_hashing',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\new_hashing.py',
   'PYMODULE'),
  ('numba.cpython.new_mathimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\new_mathimpl.py',
   'PYMODULE'),
  ('numba.cpython.new_numbers',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\new_numbers.py',
   'PYMODULE'),
  ('numba.cpython.new_tupleobj',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\new_tupleobj.py',
   'PYMODULE'),
  ('numba.cpython.numbers',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\numbers.py',
   'PYMODULE'),
  ('numba.cpython.old_builtins',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\old_builtins.py',
   'PYMODULE'),
  ('numba.cpython.old_hashing',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\old_hashing.py',
   'PYMODULE'),
  ('numba.cpython.old_mathimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\old_mathimpl.py',
   'PYMODULE'),
  ('numba.cpython.old_numbers',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\old_numbers.py',
   'PYMODULE'),
  ('numba.cpython.old_tupleobj',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\old_tupleobj.py',
   'PYMODULE'),
  ('numba.cpython.printimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\printimpl.py',
   'PYMODULE'),
  ('numba.cpython.randomimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\randomimpl.py',
   'PYMODULE'),
  ('numba.cpython.rangeobj',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\rangeobj.py',
   'PYMODULE'),
  ('numba.cpython.setobj',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\setobj.py',
   'PYMODULE'),
  ('numba.cpython.slicing',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\slicing.py',
   'PYMODULE'),
  ('numba.cpython.tupleobj',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\tupleobj.py',
   'PYMODULE'),
  ('numba.cpython.unicode',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\unicode.py',
   'PYMODULE'),
  ('numba.cpython.unicode_support',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\unicode_support.py',
   'PYMODULE'),
  ('numba.cpython.unsafe',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\unsafe\\__init__.py',
   'PYMODULE'),
  ('numba.cpython.unsafe.numbers',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\unsafe\\numbers.py',
   'PYMODULE'),
  ('numba.cpython.unsafe.tuple',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cpython\\unsafe\\tuple.py',
   'PYMODULE'),
  ('numba.cuda',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.api',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\api.py',
   'PYMODULE'),
  ('numba.cuda.api_util',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\api_util.py',
   'PYMODULE'),
  ('numba.cuda.args',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\args.py',
   'PYMODULE'),
  ('numba.cuda.cg',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cg.py',
   'PYMODULE'),
  ('numba.cuda.codegen',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\codegen.py',
   'PYMODULE'),
  ('numba.cuda.compiler',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\compiler.py',
   'PYMODULE'),
  ('numba.cuda.cuda_paths',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cuda_paths.py',
   'PYMODULE'),
  ('numba.cuda.cudadecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadecl.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.devicearray',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\devicearray.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.devices',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\devices.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.driver',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\driver.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.drvapi',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\drvapi.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.dummyarray',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\dummyarray.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.enums',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\enums.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.error',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\error.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.libs',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\libs.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.nvrtc',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\nvrtc.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.nvvm',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\nvvm.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.rtapi',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\rtapi.py',
   'PYMODULE'),
  ('numba.cuda.cudadrv.runtime',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudadrv\\runtime.py',
   'PYMODULE'),
  ('numba.cuda.cudaimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudaimpl.py',
   'PYMODULE'),
  ('numba.cuda.cudamath',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\cudamath.py',
   'PYMODULE'),
  ('numba.cuda.decorators',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\decorators.py',
   'PYMODULE'),
  ('numba.cuda.descriptor',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\descriptor.py',
   'PYMODULE'),
  ('numba.cuda.device_init',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\device_init.py',
   'PYMODULE'),
  ('numba.cuda.deviceufunc',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\deviceufunc.py',
   'PYMODULE'),
  ('numba.cuda.dispatcher',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\dispatcher.py',
   'PYMODULE'),
  ('numba.cuda.errors',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\errors.py',
   'PYMODULE'),
  ('numba.cuda.extending',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\extending.py',
   'PYMODULE'),
  ('numba.cuda.initialize',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\initialize.py',
   'PYMODULE'),
  ('numba.cuda.intrinsic_wrapper',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\intrinsic_wrapper.py',
   'PYMODULE'),
  ('numba.cuda.intrinsics',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\intrinsics.py',
   'PYMODULE'),
  ('numba.cuda.kernels',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\kernels\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.kernels.reduction',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\kernels\\reduction.py',
   'PYMODULE'),
  ('numba.cuda.kernels.transpose',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\kernels\\transpose.py',
   'PYMODULE'),
  ('numba.cuda.libdevice',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\libdevice.py',
   'PYMODULE'),
  ('numba.cuda.libdevicedecl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\libdevicedecl.py',
   'PYMODULE'),
  ('numba.cuda.libdevicefuncs',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\libdevicefuncs.py',
   'PYMODULE'),
  ('numba.cuda.libdeviceimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\libdeviceimpl.py',
   'PYMODULE'),
  ('numba.cuda.mathimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\mathimpl.py',
   'PYMODULE'),
  ('numba.cuda.models',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\models.py',
   'PYMODULE'),
  ('numba.cuda.nvvmutils',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\nvvmutils.py',
   'PYMODULE'),
  ('numba.cuda.printimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\printimpl.py',
   'PYMODULE'),
  ('numba.cuda.simulator',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.simulator.api',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\api.py',
   'PYMODULE'),
  ('numba.cuda.simulator.compiler',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\compiler.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\__init__.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.devicearray',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\devicearray.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.devices',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\devices.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.driver',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\driver.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.drvapi',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\drvapi.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.error',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\error.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.nvvm',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\nvvm.py',
   'PYMODULE'),
  ('numba.cuda.simulator.cudadrv.runtime',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\cudadrv\\runtime.py',
   'PYMODULE'),
  ('numba.cuda.simulator.kernel',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\kernel.py',
   'PYMODULE'),
  ('numba.cuda.simulator.kernelapi',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\kernelapi.py',
   'PYMODULE'),
  ('numba.cuda.simulator.reduction',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\reduction.py',
   'PYMODULE'),
  ('numba.cuda.simulator.vector_types',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator\\vector_types.py',
   'PYMODULE'),
  ('numba.cuda.simulator_init',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\simulator_init.py',
   'PYMODULE'),
  ('numba.cuda.stubs',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\stubs.py',
   'PYMODULE'),
  ('numba.cuda.target',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\target.py',
   'PYMODULE'),
  ('numba.cuda.testing',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\testing.py',
   'PYMODULE'),
  ('numba.cuda.types',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\types.py',
   'PYMODULE'),
  ('numba.cuda.ufuncs',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\ufuncs.py',
   'PYMODULE'),
  ('numba.cuda.vector_types',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\vector_types.py',
   'PYMODULE'),
  ('numba.cuda.vectorizers',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\cuda\\vectorizers.py',
   'PYMODULE'),
  ('numba.experimental',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\experimental\\__init__.py',
   'PYMODULE'),
  ('numba.experimental.function_type',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\experimental\\function_type.py',
   'PYMODULE'),
  ('numba.experimental.jitclass',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\experimental\\jitclass\\__init__.py',
   'PYMODULE'),
  ('numba.experimental.jitclass.base',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\experimental\\jitclass\\base.py',
   'PYMODULE'),
  ('numba.experimental.jitclass.boxing',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\experimental\\jitclass\\boxing.py',
   'PYMODULE'),
  ('numba.experimental.jitclass.decorators',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\experimental\\jitclass\\decorators.py',
   'PYMODULE'),
  ('numba.experimental.jitclass.overloads',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\experimental\\jitclass\\overloads.py',
   'PYMODULE'),
  ('numba.extending',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\extending.py',
   'PYMODULE'),
  ('numba.misc',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\__init__.py',
   'PYMODULE'),
  ('numba.misc.appdirs',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\appdirs.py',
   'PYMODULE'),
  ('numba.misc.cffiimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\cffiimpl.py',
   'PYMODULE'),
  ('numba.misc.coverage_support',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\coverage_support.py',
   'PYMODULE'),
  ('numba.misc.dump_style',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\dump_style.py',
   'PYMODULE'),
  ('numba.misc.findlib',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\findlib.py',
   'PYMODULE'),
  ('numba.misc.firstlinefinder',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\firstlinefinder.py',
   'PYMODULE'),
  ('numba.misc.gdb_hook',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\gdb_hook.py',
   'PYMODULE'),
  ('numba.misc.init_utils',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\init_utils.py',
   'PYMODULE'),
  ('numba.misc.inspection',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\inspection.py',
   'PYMODULE'),
  ('numba.misc.literal',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\literal.py',
   'PYMODULE'),
  ('numba.misc.llvm_pass_timings',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\llvm_pass_timings.py',
   'PYMODULE'),
  ('numba.misc.mergesort',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\mergesort.py',
   'PYMODULE'),
  ('numba.misc.quicksort',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\quicksort.py',
   'PYMODULE'),
  ('numba.misc.special',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\misc\\special.py',
   'PYMODULE'),
  ('numba.np',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\__init__.py',
   'PYMODULE'),
  ('numba.np.arraymath',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\arraymath.py',
   'PYMODULE'),
  ('numba.np.arrayobj',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\arrayobj.py',
   'PYMODULE'),
  ('numba.np.linalg',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\linalg.py',
   'PYMODULE'),
  ('numba.np.math',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\math\\__init__.py',
   'PYMODULE'),
  ('numba.np.math.cmathimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\math\\cmathimpl.py',
   'PYMODULE'),
  ('numba.np.math.mathimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\math\\mathimpl.py',
   'PYMODULE'),
  ('numba.np.math.numbers',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\math\\numbers.py',
   'PYMODULE'),
  ('numba.np.new_arraymath',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\new_arraymath.py',
   'PYMODULE'),
  ('numba.np.npdatetime',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\npdatetime.py',
   'PYMODULE'),
  ('numba.np.npdatetime_helpers',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\npdatetime_helpers.py',
   'PYMODULE'),
  ('numba.np.npyfuncs',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\npyfuncs.py',
   'PYMODULE'),
  ('numba.np.npyimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\npyimpl.py',
   'PYMODULE'),
  ('numba.np.numpy_support',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\numpy_support.py',
   'PYMODULE'),
  ('numba.np.old_arraymath',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\old_arraymath.py',
   'PYMODULE'),
  ('numba.np.polynomial',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numba.np.polynomial.polynomial_core',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\polynomial\\polynomial_core.py',
   'PYMODULE'),
  ('numba.np.polynomial.polynomial_functions',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\polynomial\\polynomial_functions.py',
   'PYMODULE'),
  ('numba.np.random',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\random\\__init__.py',
   'PYMODULE'),
  ('numba.np.random._constants',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\random\\_constants.py',
   'PYMODULE'),
  ('numba.np.random.distributions',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\random\\distributions.py',
   'PYMODULE'),
  ('numba.np.random.generator_core',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\random\\generator_core.py',
   'PYMODULE'),
  ('numba.np.random.generator_methods',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\random\\generator_methods.py',
   'PYMODULE'),
  ('numba.np.random.new_distributions',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\random\\new_distributions.py',
   'PYMODULE'),
  ('numba.np.random.new_random_methods',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\random\\new_random_methods.py',
   'PYMODULE'),
  ('numba.np.random.old_distributions',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\random\\old_distributions.py',
   'PYMODULE'),
  ('numba.np.random.old_random_methods',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\random\\old_random_methods.py',
   'PYMODULE'),
  ('numba.np.random.random_methods',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\random\\random_methods.py',
   'PYMODULE'),
  ('numba.np.ufunc',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\ufunc\\__init__.py',
   'PYMODULE'),
  ('numba.np.ufunc.array_exprs',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\ufunc\\array_exprs.py',
   'PYMODULE'),
  ('numba.np.ufunc.decorators',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\ufunc\\decorators.py',
   'PYMODULE'),
  ('numba.np.ufunc.dufunc',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\ufunc\\dufunc.py',
   'PYMODULE'),
  ('numba.np.ufunc.gufunc',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\ufunc\\gufunc.py',
   'PYMODULE'),
  ('numba.np.ufunc.parallel',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\ufunc\\parallel.py',
   'PYMODULE'),
  ('numba.np.ufunc.sigparse',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\ufunc\\sigparse.py',
   'PYMODULE'),
  ('numba.np.ufunc.ufunc_base',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\ufunc\\ufunc_base.py',
   'PYMODULE'),
  ('numba.np.ufunc.ufuncbuilder',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\ufunc\\ufuncbuilder.py',
   'PYMODULE'),
  ('numba.np.ufunc.wrappers',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\ufunc\\wrappers.py',
   'PYMODULE'),
  ('numba.np.ufunc_db',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\ufunc_db.py',
   'PYMODULE'),
  ('numba.np.unsafe',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\unsafe\\__init__.py',
   'PYMODULE'),
  ('numba.np.unsafe.ndarray',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\np\\unsafe\\ndarray.py',
   'PYMODULE'),
  ('numba.parfors',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\parfors\\__init__.py',
   'PYMODULE'),
  ('numba.parfors.array_analysis',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\parfors\\array_analysis.py',
   'PYMODULE'),
  ('numba.parfors.parfor',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\parfors\\parfor.py',
   'PYMODULE'),
  ('numba.parfors.parfor_lowering',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\parfors\\parfor_lowering.py',
   'PYMODULE'),
  ('numba.parfors.parfor_lowering_utils',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\parfors\\parfor_lowering_utils.py',
   'PYMODULE'),
  ('numba.pycc',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\pycc\\__init__.py',
   'PYMODULE'),
  ('numba.pycc.cc',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\pycc\\cc.py',
   'PYMODULE'),
  ('numba.pycc.compiler',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\pycc\\compiler.py',
   'PYMODULE'),
  ('numba.pycc.decorators',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\pycc\\decorators.py',
   'PYMODULE'),
  ('numba.pycc.llvm_types',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\pycc\\llvm_types.py',
   'PYMODULE'),
  ('numba.pycc.platform',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\pycc\\platform.py',
   'PYMODULE'),
  ('numba.runtests',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\runtests.py',
   'PYMODULE'),
  ('numba.stencils',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\stencils\\__init__.py',
   'PYMODULE'),
  ('numba.stencils.stencil',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\stencils\\stencil.py',
   'PYMODULE'),
  ('numba.stencils.stencilparfor',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\stencils\\stencilparfor.py',
   'PYMODULE'),
  ('numba.testing',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\testing\\__init__.py',
   'PYMODULE'),
  ('numba.testing._runtests',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\testing\\_runtests.py',
   'PYMODULE'),
  ('numba.testing.loader',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\testing\\loader.py',
   'PYMODULE'),
  ('numba.testing.main',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\testing\\main.py',
   'PYMODULE'),
  ('numba.tests',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\tests\\__init__.py',
   'PYMODULE'),
  ('numba.tests.support',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\tests\\support.py',
   'PYMODULE'),
  ('numba.typed',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\typed\\__init__.py',
   'PYMODULE'),
  ('numba.typed.dictimpl',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\typed\\dictimpl.py',
   'PYMODULE'),
  ('numba.typed.dictobject',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\typed\\dictobject.py',
   'PYMODULE'),
  ('numba.typed.listobject',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\typed\\listobject.py',
   'PYMODULE'),
  ('numba.typed.typeddict',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\typed\\typeddict.py',
   'PYMODULE'),
  ('numba.typed.typedlist',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\typed\\typedlist.py',
   'PYMODULE'),
  ('numba.typed.typedobjectutils',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\typed\\typedobjectutils.py',
   'PYMODULE'),
  ('numba.types',
   'C:\\Python\\Python310\\lib\\site-packages\\numba\\types\\__init__.py',
   'PYMODULE'),
  ('numbers', 'C:\\Python\\Python310\\lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Python\\Python310\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'C:\\Python\\Python310\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'C:\\Python\\Python310\\lib\\optparse.py', 'PYMODULE'),
  ('packaging',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Python\\Python310\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib', 'C:\\Python\\Python310\\lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'C:\\Python\\Python310\\lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'C:\\Python\\Python310\\lib\\pickle.py', 'PYMODULE'),
  ('pkg_resources',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.actions',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.common',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.core',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.diagram',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.exceptions',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.helpers',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.results',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.testing',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.unicode',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.util',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'C:\\Python\\Python310\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'C:\\Python\\Python310\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'C:\\Python\\Python310\\lib\\platform.py', 'PYMODULE'),
  ('plistlib', 'C:\\Python\\Python310\\lib\\plistlib.py', 'PYMODULE'),
  ('pprint', 'C:\\Python\\Python310\\lib\\pprint.py', 'PYMODULE'),
  ('profile', 'C:\\Python\\Python310\\lib\\profile.py', 'PYMODULE'),
  ('pstats', 'C:\\Python\\Python310\\lib\\pstats.py', 'PYMODULE'),
  ('py_compile', 'C:\\Python\\Python310\\lib\\py_compile.py', 'PYMODULE'),
  ('pycparser',
   'C:\\Python\\Python310\\lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'C:\\Python\\Python310\\lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'C:\\Python\\Python310\\lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'C:\\Python\\Python310\\lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'C:\\Python\\Python310\\lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'C:\\Python\\Python310\\lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'C:\\Python\\Python310\\lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'C:\\Python\\Python310\\lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'C:\\Python\\Python310\\lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'C:\\Python\\Python310\\lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'C:\\Python\\Python310\\lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc', 'C:\\Python\\Python310\\lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'C:\\Python\\Python310\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Python\\Python310\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pyparsing',
   'C:\\Python\\Python310\\lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'C:\\Python\\Python310\\lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.common',
   'C:\\Python\\Python310\\lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.core',
   'C:\\Python\\Python310\\lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'C:\\Python\\Python310\\lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'C:\\Python\\Python310\\lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'C:\\Python\\Python310\\lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pyparsing.results',
   'C:\\Python\\Python310\\lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'C:\\Python\\Python310\\lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'C:\\Python\\Python310\\lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.util',
   'C:\\Python\\Python310\\lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('queue', 'C:\\Python\\Python310\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'C:\\Python\\Python310\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Python\\Python310\\lib\\random.py', 'PYMODULE'),
  ('rlcompleter', 'C:\\Python\\Python310\\lib\\rlcompleter.py', 'PYMODULE'),
  ('runpy', 'C:\\Python\\Python310\\lib\\runpy.py', 'PYMODULE'),
  ('scipy',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\__init__.py',
   'PYMODULE'),
  ('scipy.__config__',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\__config__.py',
   'PYMODULE'),
  ('scipy._distributor_init',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_distributor_init.py',
   'PYMODULE'),
  ('scipy._lib',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\__init__.py',
   'PYMODULE'),
  ('scipy._lib._array_api',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_array_api.py',
   'PYMODULE'),
  ('scipy._lib._bunch',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_bunch.py',
   'PYMODULE'),
  ('scipy._lib._ccallback',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_ccallback.py',
   'PYMODULE'),
  ('scipy._lib._docscrape',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_docscrape.py',
   'PYMODULE'),
  ('scipy._lib._elementwise_iterative_method',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_elementwise_iterative_method.py',
   'PYMODULE'),
  ('scipy._lib._finite_differences',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_finite_differences.py',
   'PYMODULE'),
  ('scipy._lib._pep440',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_pep440.py',
   'PYMODULE'),
  ('scipy._lib._testutils',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_testutils.py',
   'PYMODULE'),
  ('scipy._lib._threadsafety',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_threadsafety.py',
   'PYMODULE'),
  ('scipy._lib._uarray',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_uarray\\__init__.py',
   'PYMODULE'),
  ('scipy._lib._uarray._backend',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_uarray\\_backend.py',
   'PYMODULE'),
  ('scipy._lib._util',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\_util.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat._internal',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\_internal.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._aliases',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._helpers',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_helpers.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._linalg',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_linalg.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.common._typing',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\common\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy._aliases',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy._info',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\_info.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.cupy._typing',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\cupy\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask.array',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\array\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask.array._aliases',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\array\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.dask.array._info',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\dask\\array\\_info.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy._aliases',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy._info',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\_info.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy._typing',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.numpy.linalg',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\numpy\\linalg.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.torch',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\torch\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.torch._aliases',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\torch\\_aliases.py',
   'PYMODULE'),
  ('scipy._lib.array_api_compat.torch._info',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_compat\\torch\\_info.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_extra\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._funcs',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_extra\\_funcs.py',
   'PYMODULE'),
  ('scipy._lib.array_api_extra._typing',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\array_api_extra\\_typing.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\cobyqa\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.framework',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\cobyqa\\framework.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.main',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\cobyqa\\main.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.models',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\cobyqa\\models.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.problem',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\cobyqa\\problem.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.settings',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\cobyqa\\settings.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.subsolvers',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\cobyqa\\subsolvers\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.subsolvers.geometry',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\cobyqa\\subsolvers\\geometry.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.subsolvers.optim',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\cobyqa\\subsolvers\\optim.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\cobyqa\\utils\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils.exceptions',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\cobyqa\\utils\\exceptions.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils.math',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\cobyqa\\utils\\math.py',
   'PYMODULE'),
  ('scipy._lib.cobyqa.utils.versions',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\cobyqa\\utils\\versions.py',
   'PYMODULE'),
  ('scipy._lib.decorator',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\decorator.py',
   'PYMODULE'),
  ('scipy._lib.deprecation',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\deprecation.py',
   'PYMODULE'),
  ('scipy._lib.doccer',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\doccer.py',
   'PYMODULE'),
  ('scipy._lib.uarray',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\_lib\\uarray.py',
   'PYMODULE'),
  ('scipy.constants',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\constants\\__init__.py',
   'PYMODULE'),
  ('scipy.constants._codata',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\constants\\_codata.py',
   'PYMODULE'),
  ('scipy.constants._constants',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\constants\\_constants.py',
   'PYMODULE'),
  ('scipy.constants.codata',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\constants\\codata.py',
   'PYMODULE'),
  ('scipy.constants.constants',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\constants\\constants.py',
   'PYMODULE'),
  ('scipy.fft',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\__init__.py',
   'PYMODULE'),
  ('scipy.fft._backend',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\_backend.py',
   'PYMODULE'),
  ('scipy.fft._basic',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\_basic.py',
   'PYMODULE'),
  ('scipy.fft._basic_backend',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\_basic_backend.py',
   'PYMODULE'),
  ('scipy.fft._fftlog',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\_fftlog.py',
   'PYMODULE'),
  ('scipy.fft._fftlog_backend',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\_fftlog_backend.py',
   'PYMODULE'),
  ('scipy.fft._helper',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\_helper.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\_pocketfft\\__init__.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.basic',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\_pocketfft\\basic.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.helper',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\_pocketfft\\helper.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.realtransforms',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\_pocketfft\\realtransforms.py',
   'PYMODULE'),
  ('scipy.fft._realtransforms',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\_realtransforms.py',
   'PYMODULE'),
  ('scipy.fft._realtransforms_backend',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\fft\\_realtransforms_backend.py',
   'PYMODULE'),
  ('scipy.integrate',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\__init__.py',
   'PYMODULE'),
  ('scipy.integrate._bvp',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_bvp.py',
   'PYMODULE'),
  ('scipy.integrate._cubature',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_cubature.py',
   'PYMODULE'),
  ('scipy.integrate._ivp',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_ivp\\__init__.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.base',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_ivp\\base.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.bdf',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_ivp\\bdf.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.common',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_ivp\\common.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.dop853_coefficients',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_ivp\\dop853_coefficients.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.ivp',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_ivp\\ivp.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.lsoda',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_ivp\\lsoda.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.radau',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_ivp\\radau.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.rk',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_ivp\\rk.py',
   'PYMODULE'),
  ('scipy.integrate._lebedev',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_lebedev.py',
   'PYMODULE'),
  ('scipy.integrate._ode',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_ode.py',
   'PYMODULE'),
  ('scipy.integrate._odepack_py',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_odepack_py.py',
   'PYMODULE'),
  ('scipy.integrate._quad_vec',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_quad_vec.py',
   'PYMODULE'),
  ('scipy.integrate._quadpack_py',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_quadpack_py.py',
   'PYMODULE'),
  ('scipy.integrate._quadrature',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_quadrature.py',
   'PYMODULE'),
  ('scipy.integrate._rules',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_rules\\__init__.py',
   'PYMODULE'),
  ('scipy.integrate._rules._base',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_rules\\_base.py',
   'PYMODULE'),
  ('scipy.integrate._rules._gauss_kronrod',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_rules\\_gauss_kronrod.py',
   'PYMODULE'),
  ('scipy.integrate._rules._gauss_legendre',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_rules\\_gauss_legendre.py',
   'PYMODULE'),
  ('scipy.integrate._rules._genz_malik',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_rules\\_genz_malik.py',
   'PYMODULE'),
  ('scipy.integrate._tanhsinh',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\_tanhsinh.py',
   'PYMODULE'),
  ('scipy.integrate.dop',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\dop.py',
   'PYMODULE'),
  ('scipy.integrate.lsoda',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\lsoda.py',
   'PYMODULE'),
  ('scipy.integrate.odepack',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\odepack.py',
   'PYMODULE'),
  ('scipy.integrate.quadpack',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\quadpack.py',
   'PYMODULE'),
  ('scipy.integrate.vode',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\integrate\\vode.py',
   'PYMODULE'),
  ('scipy.interpolate',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\__init__.py',
   'PYMODULE'),
  ('scipy.interpolate._bary_rational',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_bary_rational.py',
   'PYMODULE'),
  ('scipy.interpolate._bsplines',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_bsplines.py',
   'PYMODULE'),
  ('scipy.interpolate._cubic',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_cubic.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack2',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_fitpack2.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack_impl',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_fitpack_impl.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack_py',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_fitpack_py.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack_repro',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_fitpack_repro.py',
   'PYMODULE'),
  ('scipy.interpolate._interpolate',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_interpolate.py',
   'PYMODULE'),
  ('scipy.interpolate._ndbspline',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_ndbspline.py',
   'PYMODULE'),
  ('scipy.interpolate._ndgriddata',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_ndgriddata.py',
   'PYMODULE'),
  ('scipy.interpolate._pade',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_pade.py',
   'PYMODULE'),
  ('scipy.interpolate._polyint',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_polyint.py',
   'PYMODULE'),
  ('scipy.interpolate._rbf',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_rbf.py',
   'PYMODULE'),
  ('scipy.interpolate._rbfinterp',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_rbfinterp.py',
   'PYMODULE'),
  ('scipy.interpolate._rgi',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\_rgi.py',
   'PYMODULE'),
  ('scipy.interpolate.fitpack',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\fitpack.py',
   'PYMODULE'),
  ('scipy.interpolate.fitpack2',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\fitpack2.py',
   'PYMODULE'),
  ('scipy.interpolate.interpnd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\interpnd.py',
   'PYMODULE'),
  ('scipy.interpolate.interpolate',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\interpolate.py',
   'PYMODULE'),
  ('scipy.interpolate.ndgriddata',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\ndgriddata.py',
   'PYMODULE'),
  ('scipy.interpolate.polyint',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\polyint.py',
   'PYMODULE'),
  ('scipy.interpolate.rbf',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\interpolate\\rbf.py',
   'PYMODULE'),
  ('scipy.linalg',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\__init__.py',
   'PYMODULE'),
  ('scipy.linalg._basic',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_basic.py',
   'PYMODULE'),
  ('scipy.linalg._decomp',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_decomp.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_cholesky',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_decomp_cholesky.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_cossin',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_decomp_cossin.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_ldl',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_decomp_ldl.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_lu',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_decomp_lu.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_polar',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_decomp_polar.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_qr',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_decomp_qr.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_qz',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_decomp_qz.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_schur',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_decomp_schur.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_svd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_decomp_svd.py',
   'PYMODULE'),
  ('scipy.linalg._expm_frechet',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_expm_frechet.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_matfuncs.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs_inv_ssq',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_matfuncs_inv_ssq.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs_sqrtm',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_matfuncs_sqrtm.py',
   'PYMODULE'),
  ('scipy.linalg._misc',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_misc.py',
   'PYMODULE'),
  ('scipy.linalg._procrustes',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_procrustes.py',
   'PYMODULE'),
  ('scipy.linalg._sketches',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_sketches.py',
   'PYMODULE'),
  ('scipy.linalg._solvers',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_solvers.py',
   'PYMODULE'),
  ('scipy.linalg._special_matrices',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\_special_matrices.py',
   'PYMODULE'),
  ('scipy.linalg.basic',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\basic.py',
   'PYMODULE'),
  ('scipy.linalg.blas',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\blas.py',
   'PYMODULE'),
  ('scipy.linalg.decomp',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\decomp.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_cholesky',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\decomp_cholesky.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_lu',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\decomp_lu.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_qr',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\decomp_qr.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_schur',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\decomp_schur.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_svd',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\decomp_svd.py',
   'PYMODULE'),
  ('scipy.linalg.interpolative',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\interpolative.py',
   'PYMODULE'),
  ('scipy.linalg.lapack',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\lapack.py',
   'PYMODULE'),
  ('scipy.linalg.matfuncs',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\matfuncs.py',
   'PYMODULE'),
  ('scipy.linalg.misc',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\misc.py',
   'PYMODULE'),
  ('scipy.linalg.special_matrices',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\linalg\\special_matrices.py',
   'PYMODULE'),
  ('scipy.ndimage',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\__init__.py',
   'PYMODULE'),
  ('scipy.ndimage._delegators',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\_delegators.py',
   'PYMODULE'),
  ('scipy.ndimage._filters',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\_filters.py',
   'PYMODULE'),
  ('scipy.ndimage._fourier',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\_fourier.py',
   'PYMODULE'),
  ('scipy.ndimage._interpolation',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\_interpolation.py',
   'PYMODULE'),
  ('scipy.ndimage._measurements',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\_measurements.py',
   'PYMODULE'),
  ('scipy.ndimage._morphology',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\_morphology.py',
   'PYMODULE'),
  ('scipy.ndimage._ndimage_api',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\_ndimage_api.py',
   'PYMODULE'),
  ('scipy.ndimage._ni_docstrings',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\_ni_docstrings.py',
   'PYMODULE'),
  ('scipy.ndimage._ni_support',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\_ni_support.py',
   'PYMODULE'),
  ('scipy.ndimage._support_alternative_backends',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\_support_alternative_backends.py',
   'PYMODULE'),
  ('scipy.ndimage.filters',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\filters.py',
   'PYMODULE'),
  ('scipy.ndimage.fourier',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\fourier.py',
   'PYMODULE'),
  ('scipy.ndimage.interpolation',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\interpolation.py',
   'PYMODULE'),
  ('scipy.ndimage.measurements',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\measurements.py',
   'PYMODULE'),
  ('scipy.ndimage.morphology',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\ndimage\\morphology.py',
   'PYMODULE'),
  ('scipy.optimize',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._basinhopping',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_basinhopping.py',
   'PYMODULE'),
  ('scipy.optimize._bracket',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_bracket.py',
   'PYMODULE'),
  ('scipy.optimize._chandrupatla',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_chandrupatla.py',
   'PYMODULE'),
  ('scipy.optimize._cobyla_py',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_cobyla_py.py',
   'PYMODULE'),
  ('scipy.optimize._cobyqa_py',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_cobyqa_py.py',
   'PYMODULE'),
  ('scipy.optimize._constraints',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_constraints.py',
   'PYMODULE'),
  ('scipy.optimize._dcsrch',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_dcsrch.py',
   'PYMODULE'),
  ('scipy.optimize._differentiable_functions',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_differentiable_functions.py',
   'PYMODULE'),
  ('scipy.optimize._differentialevolution',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_differentialevolution.py',
   'PYMODULE'),
  ('scipy.optimize._direct_py',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_direct_py.py',
   'PYMODULE'),
  ('scipy.optimize._dual_annealing',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_dual_annealing.py',
   'PYMODULE'),
  ('scipy.optimize._hessian_update_strategy',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_hessian_update_strategy.py',
   'PYMODULE'),
  ('scipy.optimize._highspy',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_highspy\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._highspy._highs_wrapper',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_highspy\\_highs_wrapper.py',
   'PYMODULE'),
  ('scipy.optimize._isotonic',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_isotonic.py',
   'PYMODULE'),
  ('scipy.optimize._lbfgsb_py',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_lbfgsb_py.py',
   'PYMODULE'),
  ('scipy.optimize._linesearch',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_linesearch.py',
   'PYMODULE'),
  ('scipy.optimize._linprog',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_linprog.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_doc',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_linprog_doc.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_highs',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_linprog_highs.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_ip',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_linprog_ip.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_rs',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_linprog_rs.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_simplex',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_linprog_simplex.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_util',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_linprog_util.py',
   'PYMODULE'),
  ('scipy.optimize._lsq',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_lsq\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.bvls',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_lsq\\bvls.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.common',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_lsq\\common.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.dogbox',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_lsq\\dogbox.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.least_squares',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_lsq\\least_squares.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.lsq_linear',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_lsq\\lsq_linear.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.trf',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_lsq\\trf.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.trf_linear',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_lsq\\trf_linear.py',
   'PYMODULE'),
  ('scipy.optimize._milp',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_milp.py',
   'PYMODULE'),
  ('scipy.optimize._minimize',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_minimize.py',
   'PYMODULE'),
  ('scipy.optimize._minpack_py',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_minpack_py.py',
   'PYMODULE'),
  ('scipy.optimize._nnls',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_nnls.py',
   'PYMODULE'),
  ('scipy.optimize._nonlin',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_nonlin.py',
   'PYMODULE'),
  ('scipy.optimize._numdiff',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_numdiff.py',
   'PYMODULE'),
  ('scipy.optimize._optimize',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_optimize.py',
   'PYMODULE'),
  ('scipy.optimize._qap',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_qap.py',
   'PYMODULE'),
  ('scipy.optimize._remove_redundancy',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_remove_redundancy.py',
   'PYMODULE'),
  ('scipy.optimize._root',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_root.py',
   'PYMODULE'),
  ('scipy.optimize._root_scalar',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_root_scalar.py',
   'PYMODULE'),
  ('scipy.optimize._shgo',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_shgo.py',
   'PYMODULE'),
  ('scipy.optimize._shgo_lib',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_shgo_lib\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._shgo_lib._complex',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_shgo_lib\\_complex.py',
   'PYMODULE'),
  ('scipy.optimize._shgo_lib._vertex',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_shgo_lib\\_vertex.py',
   'PYMODULE'),
  ('scipy.optimize._slsqp_py',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_slsqp_py.py',
   'PYMODULE'),
  ('scipy.optimize._spectral',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_spectral.py',
   'PYMODULE'),
  ('scipy.optimize._tnc',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_tnc.py',
   'PYMODULE'),
  ('scipy.optimize._trlib',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trlib\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trustregion.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.canonical_constraint',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\canonical_constraint.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.equality_constrained_sqp',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\equality_constrained_sqp.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.minimize_trustregion_constr',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\minimize_trustregion_constr.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.projections',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\projections.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.qp_subproblem',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\qp_subproblem.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.report',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\report.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.tr_interior_point',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\tr_interior_point.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_dogleg',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trustregion_dogleg.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_exact',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trustregion_exact.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_krylov',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trustregion_krylov.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_ncg',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_trustregion_ncg.py',
   'PYMODULE'),
  ('scipy.optimize._zeros_py',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\_zeros_py.py',
   'PYMODULE'),
  ('scipy.optimize.cobyla',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\cobyla.py',
   'PYMODULE'),
  ('scipy.optimize.lbfgsb',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\lbfgsb.py',
   'PYMODULE'),
  ('scipy.optimize.linesearch',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\linesearch.py',
   'PYMODULE'),
  ('scipy.optimize.minpack',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\minpack.py',
   'PYMODULE'),
  ('scipy.optimize.minpack2',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\minpack2.py',
   'PYMODULE'),
  ('scipy.optimize.moduleTNC',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\moduleTNC.py',
   'PYMODULE'),
  ('scipy.optimize.nonlin',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\nonlin.py',
   'PYMODULE'),
  ('scipy.optimize.optimize',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\optimize.py',
   'PYMODULE'),
  ('scipy.optimize.slsqp',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\slsqp.py',
   'PYMODULE'),
  ('scipy.optimize.tnc',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\tnc.py',
   'PYMODULE'),
  ('scipy.optimize.zeros',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\optimize\\zeros.py',
   'PYMODULE'),
  ('scipy.sparse',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse._base',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_base.py',
   'PYMODULE'),
  ('scipy.sparse._bsr',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_bsr.py',
   'PYMODULE'),
  ('scipy.sparse._compressed',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_compressed.py',
   'PYMODULE'),
  ('scipy.sparse._construct',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_construct.py',
   'PYMODULE'),
  ('scipy.sparse._coo',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_coo.py',
   'PYMODULE'),
  ('scipy.sparse._csc',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_csc.py',
   'PYMODULE'),
  ('scipy.sparse._csr',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_csr.py',
   'PYMODULE'),
  ('scipy.sparse._data',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_data.py',
   'PYMODULE'),
  ('scipy.sparse._dia',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_dia.py',
   'PYMODULE'),
  ('scipy.sparse._dok',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_dok.py',
   'PYMODULE'),
  ('scipy.sparse._extract',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_extract.py',
   'PYMODULE'),
  ('scipy.sparse._index',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_index.py',
   'PYMODULE'),
  ('scipy.sparse._lil',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_lil.py',
   'PYMODULE'),
  ('scipy.sparse._matrix',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_matrix.py',
   'PYMODULE'),
  ('scipy.sparse._matrix_io',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_matrix_io.py',
   'PYMODULE'),
  ('scipy.sparse._spfuncs',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_spfuncs.py',
   'PYMODULE'),
  ('scipy.sparse._sputils',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\_sputils.py',
   'PYMODULE'),
  ('scipy.sparse.base',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\base.py',
   'PYMODULE'),
  ('scipy.sparse.bsr',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\bsr.py',
   'PYMODULE'),
  ('scipy.sparse.compressed',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\compressed.py',
   'PYMODULE'),
  ('scipy.sparse.construct',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\construct.py',
   'PYMODULE'),
  ('scipy.sparse.coo',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\coo.py',
   'PYMODULE'),
  ('scipy.sparse.csc',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\csc.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\csgraph\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph._laplacian',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\csgraph\\_laplacian.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph._validation',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\csgraph\\_validation.py',
   'PYMODULE'),
  ('scipy.sparse.csr',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\csr.py',
   'PYMODULE'),
  ('scipy.sparse.data',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\data.py',
   'PYMODULE'),
  ('scipy.sparse.dia',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\dia.py',
   'PYMODULE'),
  ('scipy.sparse.dok',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\dok.py',
   'PYMODULE'),
  ('scipy.sparse.extract',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\extract.py',
   'PYMODULE'),
  ('scipy.sparse.lil',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\lil.py',
   'PYMODULE'),
  ('scipy.sparse.linalg',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve._add_newdocs',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\_add_newdocs.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve.linsolve',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\linsolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen._svds',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\_svds.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.arpack',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.arpack.arpack',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\arpack.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.lobpcg',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\lobpcg\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.lobpcg.lobpcg',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\lobpcg\\lobpcg.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._expm_multiply',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_expm_multiply.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._interface',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_interface.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve._gcrotmk',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\_gcrotmk.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.iterative',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\iterative.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lgmres',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lgmres.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lsmr',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lsmr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lsqr',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lsqr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.minres',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\minres.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.tfqmr',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\tfqmr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.utils',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\utils.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._matfuncs',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_matfuncs.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._norm',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_norm.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._onenormest',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_onenormest.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._propack', '-', 'PYMODULE'),
  ('scipy.sparse.linalg._special_sparse_arrays',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_special_sparse_arrays.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._svdp',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\_svdp.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.dsolve',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\dsolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.eigen',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\eigen.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.interface',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\interface.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.isolve',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\isolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.matfuncs',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\linalg\\matfuncs.py',
   'PYMODULE'),
  ('scipy.sparse.sparsetools',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\sparsetools.py',
   'PYMODULE'),
  ('scipy.sparse.sputils',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\sparse\\sputils.py',
   'PYMODULE'),
  ('scipy.spatial',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\__init__.py',
   'PYMODULE'),
  ('scipy.spatial._geometric_slerp',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\_geometric_slerp.py',
   'PYMODULE'),
  ('scipy.spatial._kdtree',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\_kdtree.py',
   'PYMODULE'),
  ('scipy.spatial._plotutils',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\_plotutils.py',
   'PYMODULE'),
  ('scipy.spatial._procrustes',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\_procrustes.py',
   'PYMODULE'),
  ('scipy.spatial._spherical_voronoi',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\_spherical_voronoi.py',
   'PYMODULE'),
  ('scipy.spatial.ckdtree',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\ckdtree.py',
   'PYMODULE'),
  ('scipy.spatial.distance',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\distance.py',
   'PYMODULE'),
  ('scipy.spatial.kdtree',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\kdtree.py',
   'PYMODULE'),
  ('scipy.spatial.qhull',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\qhull.py',
   'PYMODULE'),
  ('scipy.spatial.transform',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\transform\\__init__.py',
   'PYMODULE'),
  ('scipy.spatial.transform._rotation_groups',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\transform\\_rotation_groups.py',
   'PYMODULE'),
  ('scipy.spatial.transform._rotation_spline',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\transform\\_rotation_spline.py',
   'PYMODULE'),
  ('scipy.spatial.transform.rotation',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\spatial\\transform\\rotation.py',
   'PYMODULE'),
  ('scipy.special',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\__init__.py',
   'PYMODULE'),
  ('scipy.special._basic',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_basic.py',
   'PYMODULE'),
  ('scipy.special._ellip_harm',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_ellip_harm.py',
   'PYMODULE'),
  ('scipy.special._input_validation',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_input_validation.py',
   'PYMODULE'),
  ('scipy.special._lambertw',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_lambertw.py',
   'PYMODULE'),
  ('scipy.special._logsumexp',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_logsumexp.py',
   'PYMODULE'),
  ('scipy.special._multiufuncs',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_multiufuncs.py',
   'PYMODULE'),
  ('scipy.special._orthogonal',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_orthogonal.py',
   'PYMODULE'),
  ('scipy.special._sf_error',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_sf_error.py',
   'PYMODULE'),
  ('scipy.special._spfun_stats',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_spfun_stats.py',
   'PYMODULE'),
  ('scipy.special._spherical_bessel',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_spherical_bessel.py',
   'PYMODULE'),
  ('scipy.special._support_alternative_backends',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\_support_alternative_backends.py',
   'PYMODULE'),
  ('scipy.special.add_newdocs',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\add_newdocs.py',
   'PYMODULE'),
  ('scipy.special.basic',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\basic.py',
   'PYMODULE'),
  ('scipy.special.orthogonal',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\orthogonal.py',
   'PYMODULE'),
  ('scipy.special.sf_error',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\sf_error.py',
   'PYMODULE'),
  ('scipy.special.specfun',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\specfun.py',
   'PYMODULE'),
  ('scipy.special.spfun_stats',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\special\\spfun_stats.py',
   'PYMODULE'),
  ('scipy.stats',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\__init__.py',
   'PYMODULE'),
  ('scipy.stats._axis_nan_policy',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_axis_nan_policy.py',
   'PYMODULE'),
  ('scipy.stats._binned_statistic',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_binned_statistic.py',
   'PYMODULE'),
  ('scipy.stats._binomtest',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_binomtest.py',
   'PYMODULE'),
  ('scipy.stats._bws_test',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_bws_test.py',
   'PYMODULE'),
  ('scipy.stats._censored_data',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_censored_data.py',
   'PYMODULE'),
  ('scipy.stats._common',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_common.py',
   'PYMODULE'),
  ('scipy.stats._constants',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_constants.py',
   'PYMODULE'),
  ('scipy.stats._continuous_distns',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_continuous_distns.py',
   'PYMODULE'),
  ('scipy.stats._correlation',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_correlation.py',
   'PYMODULE'),
  ('scipy.stats._covariance',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_covariance.py',
   'PYMODULE'),
  ('scipy.stats._crosstab',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_crosstab.py',
   'PYMODULE'),
  ('scipy.stats._discrete_distns',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_discrete_distns.py',
   'PYMODULE'),
  ('scipy.stats._distn_infrastructure',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_distn_infrastructure.py',
   'PYMODULE'),
  ('scipy.stats._distr_params',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_distr_params.py',
   'PYMODULE'),
  ('scipy.stats._distribution_infrastructure',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_distribution_infrastructure.py',
   'PYMODULE'),
  ('scipy.stats._entropy',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_entropy.py',
   'PYMODULE'),
  ('scipy.stats._fit',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_fit.py',
   'PYMODULE'),
  ('scipy.stats._hypotests',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_hypotests.py',
   'PYMODULE'),
  ('scipy.stats._kde',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_kde.py',
   'PYMODULE'),
  ('scipy.stats._ksstats',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_ksstats.py',
   'PYMODULE'),
  ('scipy.stats._levy_stable',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_levy_stable\\__init__.py',
   'PYMODULE'),
  ('scipy.stats._mannwhitneyu',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_mannwhitneyu.py',
   'PYMODULE'),
  ('scipy.stats._mgc',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_mgc.py',
   'PYMODULE'),
  ('scipy.stats._morestats',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_morestats.py',
   'PYMODULE'),
  ('scipy.stats._mstats_basic',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_mstats_basic.py',
   'PYMODULE'),
  ('scipy.stats._mstats_extras',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_mstats_extras.py',
   'PYMODULE'),
  ('scipy.stats._multicomp',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_multicomp.py',
   'PYMODULE'),
  ('scipy.stats._multivariate',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_multivariate.py',
   'PYMODULE'),
  ('scipy.stats._new_distributions',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_new_distributions.py',
   'PYMODULE'),
  ('scipy.stats._odds_ratio',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_odds_ratio.py',
   'PYMODULE'),
  ('scipy.stats._page_trend_test',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_page_trend_test.py',
   'PYMODULE'),
  ('scipy.stats._probability_distribution',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_probability_distribution.py',
   'PYMODULE'),
  ('scipy.stats._qmc',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_qmc.py',
   'PYMODULE'),
  ('scipy.stats._qmvnt',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_qmvnt.py',
   'PYMODULE'),
  ('scipy.stats._rcont',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_rcont\\__init__.py',
   'PYMODULE'),
  ('scipy.stats._relative_risk',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_relative_risk.py',
   'PYMODULE'),
  ('scipy.stats._resampling',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_resampling.py',
   'PYMODULE'),
  ('scipy.stats._sensitivity_analysis',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_sensitivity_analysis.py',
   'PYMODULE'),
  ('scipy.stats._stats_mstats_common',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_stats_mstats_common.py',
   'PYMODULE'),
  ('scipy.stats._stats_py',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_stats_py.py',
   'PYMODULE'),
  ('scipy.stats._survival',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_survival.py',
   'PYMODULE'),
  ('scipy.stats._tukeylambda_stats',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_tukeylambda_stats.py',
   'PYMODULE'),
  ('scipy.stats._variation',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_variation.py',
   'PYMODULE'),
  ('scipy.stats._warnings_errors',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_warnings_errors.py',
   'PYMODULE'),
  ('scipy.stats._wilcoxon',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\_wilcoxon.py',
   'PYMODULE'),
  ('scipy.stats.biasedurn',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\biasedurn.py',
   'PYMODULE'),
  ('scipy.stats.contingency',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\contingency.py',
   'PYMODULE'),
  ('scipy.stats.distributions',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\distributions.py',
   'PYMODULE'),
  ('scipy.stats.kde',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\kde.py',
   'PYMODULE'),
  ('scipy.stats.morestats',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\morestats.py',
   'PYMODULE'),
  ('scipy.stats.mstats',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\mstats.py',
   'PYMODULE'),
  ('scipy.stats.mstats_basic',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\mstats_basic.py',
   'PYMODULE'),
  ('scipy.stats.mstats_extras',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\mstats_extras.py',
   'PYMODULE'),
  ('scipy.stats.mvn',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\mvn.py',
   'PYMODULE'),
  ('scipy.stats.qmc',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\qmc.py',
   'PYMODULE'),
  ('scipy.stats.stats',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\stats\\stats.py',
   'PYMODULE'),
  ('scipy.version',
   'C:\\Python\\Python310\\lib\\site-packages\\scipy\\version.py',
   'PYMODULE'),
  ('secrets', 'C:\\Python\\Python310\\lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'C:\\Python\\Python310\\lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._deprecation_warning',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_deprecation_warning.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\_collections.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_msi',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_msi.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_wininst',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_wininst.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.py37compat',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\py37compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.register',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\register.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\command\\upload.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.py38compat',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\py39compat.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.__about__',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.actions',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.common',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.core',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.diagram',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.exceptions',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.helpers',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.results',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.testing',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.unicode',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.util',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\_vendor\\zipp.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.build_ext',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.extern',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\py34compat.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Python\\Python310\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'C:\\Python\\Python310\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'C:\\Python\\Python310\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Python\\Python310\\lib\\signal.py', 'PYMODULE'),
  ('site', 'C:\\Python\\Python310\\lib\\site.py', 'PYMODULE'),
  ('six', 'C:\\Python\\Python310\\lib\\site-packages\\six.py', 'PYMODULE'),
  ('socket', 'C:\\Python\\Python310\\lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'C:\\Python\\Python310\\lib\\socketserver.py', 'PYMODULE'),
  ('ssl', 'C:\\Python\\Python310\\lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'C:\\Python\\Python310\\lib\\statistics.py', 'PYMODULE'),
  ('string', 'C:\\Python\\Python310\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'C:\\Python\\Python310\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'C:\\Python\\Python310\\lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'C:\\Python\\Python310\\lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'C:\\Python\\Python310\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\Python\\Python310\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\Python\\Python310\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Python\\Python310\\lib\\threading.py', 'PYMODULE'),
  ('timeit', 'C:\\Python\\Python310\\lib\\timeit.py', 'PYMODULE'),
  ('tkinter', 'C:\\Python\\Python310\\lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Python\\Python310\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Python\\Python310\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'C:\\Python\\Python310\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'C:\\Python\\Python310\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.font', 'C:\\Python\\Python310\\lib\\tkinter\\font.py', 'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Python\\Python310\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'C:\\Python\\Python310\\lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk', 'C:\\Python\\Python310\\lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('token', 'C:\\Python\\Python310\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Python\\Python310\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'C:\\Python\\Python310\\lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'C:\\Python\\Python310\\lib\\tty.py', 'PYMODULE'),
  ('typing', 'C:\\Python\\Python310\\lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'C:\\Python\\Python310\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest', 'C:\\Python\\Python310\\lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest._log',
   'C:\\Python\\Python310\\lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Python\\Python310\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Python\\Python310\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Python\\Python310\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Python\\Python310\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Python\\Python310\\lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Python\\Python310\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Python\\Python310\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Python\\Python310\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Python\\Python310\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Python\\Python310\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib', 'C:\\Python\\Python310\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'C:\\Python\\Python310\\lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'C:\\Python\\Python310\\lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request',
   'C:\\Python\\Python310\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Python\\Python310\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('uu', 'C:\\Python\\Python310\\lib\\uu.py', 'PYMODULE'),
  ('uuid', 'C:\\Python\\Python310\\lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'C:\\Python\\Python310\\lib\\webbrowser.py', 'PYMODULE'),
  ('xml', 'C:\\Python\\Python310\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.etree',
   'C:\\Python\\Python310\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Python\\Python310\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Python\\Python310\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Python\\Python310\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Python\\Python310\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Python\\Python310\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Python\\Python310\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax', 'C:\\Python\\Python310\\lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Python\\Python310\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Python\\Python310\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Python\\Python310\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Python\\Python310\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Python\\Python310\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'C:\\Python\\Python310\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Python\\Python310\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile', 'C:\\Python\\Python310\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'C:\\Python\\Python310\\lib\\zipimport.py', 'PYMODULE')])
